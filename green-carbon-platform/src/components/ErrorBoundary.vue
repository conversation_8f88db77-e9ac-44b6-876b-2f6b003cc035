<template>
  <div v-if="hasError" class="error-boundary">
    <a-result
      status="error"
      title="页面加载出错"
      :sub-title="errorMessage"
    >
      <template #extra>
        <a-button type="primary" @click="retry">
          重新加载
        </a-button>
        <a-button @click="goHome">
          返回首页
        </a-button>
      </template>
    </a-result>
  </div>
  <div v-else>
    <slot />
  </div>
</template>

<script setup>
import { ref, onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const hasError = ref(false)
const errorMessage = ref('')

onErrorCaptured((error, instance, info) => {
  console.error('Error captured:', error, info)
  hasError.value = true
  errorMessage.value = error.message || '未知错误'
  return false
})

const retry = () => {
  hasError.value = false
  errorMessage.value = ''
  window.location.reload()
}

const goHome = () => {
  hasError.value = false
  errorMessage.value = ''
  router.push('/')
}
</script>

<style scoped>
.error-boundary {
  padding: 50px;
  text-align: center;
}
</style>
