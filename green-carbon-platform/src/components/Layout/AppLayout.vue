<template>
  <a-layout class="green-carbon-layout">
    <!-- 头部导航 -->
    <AppHeader />
    
    <!-- 主内容区域 -->
    <a-layout class="site-layout">
      <!-- 侧边栏 -->
      <a-layout-sider 
        v-model:collapsed="collapsed" 
        :trigger="null" 
        collapsible
        :width="200"
        :collapsed-width="80"
        class="layout-sider"
      >
        <div class="logo">
          <span class="logo-icon">🌱</span>
          <span v-show="!collapsed" class="logo-text">绿色低碳</span>
        </div>
        
        <a-menu
          v-model:selectedKeys="selectedKeys"
          mode="inline"
          theme="light"
          class="sidebar-menu"
        >
          <a-menu-item key="/" @click="$router.push('/')">
            <HomeOutlined />
            <span>首页Dashboard</span>
          </a-menu-item>
          
          <a-menu-item key="/resources" @click="$router.push('/resources')">
            <DatabaseOutlined />
            <span>信息资源管理</span>
          </a-menu-item>
          
          <a-menu-item key="/policy" @click="$router.push('/policy')">
            <FileTextOutlined />
            <span>政策法规服务</span>
          </a-menu-item>
          
          <a-menu-item key="/finance" @click="$router.push('/finance')">
            <DollarOutlined />
            <span>绿色金融中心</span>
          </a-menu-item>
          
          <a-menu-item key="/technology" @click="$router.push('/technology')">
            <ExperimentOutlined />
            <span>绿色技术创新</span>
          </a-menu-item>
          
          <a-menu-item key="/supply-chain" @click="$router.push('/supply-chain')">
            <ShareAltOutlined />
            <span>供应链服务</span>
          </a-menu-item>
          
          <a-menu-item key="/benchmark" @click="$router.push('/benchmark')">
            <TrophyOutlined />
            <span>标杆示范展示</span>
          </a-menu-item>
          
          <a-menu-item key="/innovation" @click="$router.push('/innovation')">
            <BulbOutlined />
            <span>综合创新服务</span>
          </a-menu-item>
          
          <a-menu-item key="/ai-analysis" @click="$router.push('/ai-analysis')">
            <RobotOutlined />
            <span>AI智能分析</span>
          </a-menu-item>
          
          <a-menu-item key="/data-analysis" @click="$router.push('/data-analysis')">
            <BarChartOutlined />
            <span>数据分析决策</span>
          </a-menu-item>
          
          <a-menu-item key="/upload" @click="$router.push('/upload')">
            <UploadOutlined />
            <span>文件上传管理</span>
          </a-menu-item>

          <a-menu-item key="/test" @click="$router.push('/test')">
            <span>🔧</span>
            <span>测试页面</span>
          </a-menu-item>
        </a-menu>
      </a-layout-sider>
      
      <!-- 内容区域 -->
      <a-layout-content class="layout-content">
        <div class="content-wrapper">
          <ErrorBoundary>
            <router-view v-slot="{ Component }">
              <transition name="fade" mode="out-in">
                <component :is="Component" />
              </transition>
            </router-view>
          </ErrorBoundary>
        </div>
      </a-layout-content>
    </a-layout>
    
    <!-- 底部 -->
    <a-layout-footer class="layout-footer">
      <div class="footer-content">
        © 2024 国家绿色低碳全产业链服务平台 | 基于Vue3 + Ant Design Vue构建
      </div>
    </a-layout-footer>
  </a-layout>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import {
  HomeOutlined,
  DatabaseOutlined,
  FileTextOutlined,
  DollarOutlined,
  ExperimentOutlined,
  ShareAltOutlined,
  TrophyOutlined,
  BulbOutlined,
  RobotOutlined,
  BarChartOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'
import AppHeader from './AppHeader.vue'
import ErrorBoundary from '../ErrorBoundary.vue'

const route = useRoute()
const collapsed = ref(false)
const selectedKeys = ref([route.path])

// 监听路由变化，更新选中的菜单项
watch(() => route.path, (newPath) => {
  selectedKeys.value = [newPath]
})
</script>

<style scoped>
.green-carbon-layout {
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

.site-layout {
  flex: 1;
  display: flex;
  width: 100%;
  margin: 0;
  padding: 0;
}

.layout-sider {
  background: #ffffff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
  flex-shrink: 0;
  width: 200px !important;
  min-width: 200px !important;
  max-width: 200px !important;
  height: calc(100vh - 64px);
  overflow-y: auto;
}

.layout-sider.ant-layout-sider-collapsed {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
}

.logo {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #2c5f2d 0%, #97bc62 100%);
  color: white;
  position: sticky;
  top: 0;
  z-index: 2;
}

.logo-icon {
  font-size: 20px;
  margin-right: 6px;
  animation: pulse 2s ease-in-out infinite alternate;
}

.logo-text {
  font-size: 14px;
  font-weight: 600;
}

.sidebar-menu {
  border-right: none;
  background: #ffffff;
  padding: 4px 0;
  height: calc(100% - 50px);
  overflow-y: auto;
}

.sidebar-menu .ant-menu-item {
  margin: 2px 6px;
  border-radius: 8px;
  height: 40px;
  line-height: 40px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #595959;
  border: none;
}

.sidebar-menu .ant-menu-item:hover {
  background: linear-gradient(135deg, rgba(44, 95, 45, 0.1) 0%, rgba(151, 188, 98, 0.1) 100%);
  color: #2c5f2d;
  transform: translateX(2px);
}

.sidebar-menu .ant-menu-item-selected {
  background: linear-gradient(135deg, #2c5f2d 0%, #689a3d 50%, #97bc62 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(44, 95, 45, 0.3);
}

.sidebar-menu .ant-menu-item-selected::after {
  display: none;
}

.sidebar-menu .ant-menu-item-selected:hover {
  background: linear-gradient(135deg, #2c5f2d 0%, #689a3d 50%, #97bc62 100%);
  color: white;
  transform: translateX(0);
}

.sidebar-menu .ant-menu-item .anticon {
  font-size: 16px;
  margin-right: 8px;
  transition: all 0.3s ease;
}

.sidebar-menu .ant-menu-item:hover .anticon {
  color: #2c5f2d;
}

.sidebar-menu .ant-menu-item-selected .anticon {
  color: white;
}

.layout-content {
  flex: 1;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  width: calc(100vw - 200px);
  min-width: 0;
  margin: 0;
  padding: 0;
  height: calc(100vh - 64px);
  overflow: hidden;
}

.content-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  margin: 0;
  background: #f8f9fa;
  width: 100%;
  min-width: 0;
  max-width: 100%;
  box-sizing: border-box;
}

.layout-footer {
  background: #ffffff;
  border-top: 1px solid #e8e8e8;
  padding: 12px 24px;
  text-align: center;
  flex-shrink: 0;
  width: 100%;
  margin: 0;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-content {
  color: #8c8c8c;
  font-size: 12px;
  margin: 0;
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Logo动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

/* 滚动条样式 */
.sidebar-menu::-webkit-scrollbar,
.content-wrapper::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track,
.content-wrapper::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.sidebar-menu::-webkit-scrollbar-thumb,
.content-wrapper::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover,
.content-wrapper::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .site-layout {
    flex-direction: column;
  }

  .layout-sider {
    position: fixed;
    height: 100vh;
    z-index: 200;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    width: 200px !important;
    min-width: 200px !important;
    max-width: 200px !important;
  }

  .layout-sider.collapsed {
    transform: translateX(0);
  }

  .layout-content {
    margin-left: 0;
    width: 100vw;
    height: calc(100vh - 64px);
  }

  .content-wrapper {
    width: 100vw;
    max-width: 100vw;
  }

  .sidebar-menu {
    height: calc(100vh - 64px);
  }
}

@media (max-width: 480px) {
  .footer-content {
    font-size: 10px;
  }
  
  .logo-text {
    display: none;
  }
}
</style> 