<template>
  <a-layout-header class="app-header">
    <div class="header-container">
      <div class="logo-section">
        <span class="logo-icon">🌱</span>
        <h1 class="site-title">国家绿色低碳全产业链服务平台</h1>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-button type="primary" ghost @click="$router.push('/upload')" class="upload-btn">
            <UploadOutlined />
            上传文档
          </a-button>
          <a-dropdown>
            <a-button class="user-btn">
              <UserOutlined />
              用户中心
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人信息
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  系统设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </div>
  </a-layout-header>
</template>

<script setup>
import { 
  UploadOutlined, 
  UserOutlined, 
  DownOutlined,
  SettingOutlined,
  LogoutOutlined 
} from '@ant-design/icons-vue'
</script>

<style scoped>
.green-carbon-header {
  background: linear-gradient(135deg, #2c5f2d 0%, #689a3d 50%, #97bc62 100%);
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(44, 95, 45, 0.15);
  position: relative;
  z-index: 100;
  width: 100%;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.header-title {
  color: white;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 24px;
  animation: pulse 2s ease-in-out infinite alternate;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 400px;
  margin: 0 20px;
}

.global-search {
  width: 100%;
  max-width: 300px;
}

.search-input {
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  height: 36px;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-btn {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.breadcrumb-container {
  position: absolute;
  bottom: 0;
  left: 24px;
  right: 24px;
  height: 32px;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px 16px 0 0;
  padding: 0 16px;
  backdrop-filter: blur(10px);
}

.breadcrumb-nav {
  color: white;
}

.breadcrumb-nav :deep(.ant-breadcrumb-link) {
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.breadcrumb-nav :deep(.ant-breadcrumb-link:hover) {
  color: white;
}

.breadcrumb-nav :deep(.ant-breadcrumb-separator) {
  color: rgba(255, 255, 255, 0.6);
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .green-carbon-header {
    padding: 0 16px;
  }
  
  .header-center {
    display: none;
  }
  
  .header-title {
    font-size: 16px;
  }
  
  .title-icon {
    font-size: 20px;
  }
  
  .header-actions {
    gap: 8px;
  }
  
  .action-btn {
    width: 32px;
    height: 32px;
  }
  
  .user-name {
    display: none;
  }
  
  .breadcrumb-container {
    left: 16px;
    right: 16px;
  }
}

@media (max-width: 480px) {
  .green-carbon-header {
    padding: 0 12px;
  }
  
  .header-title {
    font-size: 14px;
  }
  
  .header-actions {
    gap: 6px;
  }
  
  .action-btn {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
  
  .user-info {
    padding: 4px 8px;
  }
  
  .user-avatar {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
}
</style> 