// 法律法规层级体系 (基于《中华人民共和国立法法》2023修正)

export const LEGAL_HIERARCHY = [
  {
    level: 'constitution',
    name: '宪法及基本法律',
    description: '生态环境保护基本原则',
    status: '现行有效',
    examples: ['《中华人民共和国宪法》第九条、第二十六条'],
    count: 12,
    color: '#f5222d',
    icon: 'CrownOutlined',
    authority: '全国人民代表大会',
    effectLevel: '最高'
  },
  {
    level: 'national_law',
    name: '国家法律',
    description: '环境保护、气候变化应对等',
    status: '现行有效',
    examples: ['《环境保护法》', '《气候变化应对法》', '《节约能源法》'],
    count: 45,
    color: '#fa541c',
    icon: 'BookOutlined',
    authority: '全国人民代表大会/全国人大常委会',
    effectLevel: '高'
  },
  {
    level: 'regulation',
    name: '行政法规',
    description: '碳排放权交易管理等',
    status: '部分修订中',
    examples: ['《碳排放权交易管理和条例》', '《重点用能单位节能管理办法》'],
    count: 78,
    color: '#faad14',
    icon: 'FileTextOutlined',
    authority: '国务院',
    effectLevel: '中'
  },
  {
    level: 'international',
    name: '国际公约',
    description: '国际环境公约',
    status: '履约中',
    examples: ['《联合国气候变化框架公约》', '《生物多样性公约》', '《巴黎协定》'],
    count: 23,
    color: '#52c41a',
    icon: 'GlobalOutlined',
    authority: '国际组织',
    effectLevel: '国际'
  }
]

// 法律效力状态
export const LEGAL_STATUS = [
  {
    status: 'current',
    name: '现行有效',
    description: '当前有效执行的法律法规',
    color: '#52c41a',
    icon: 'CheckCircleOutlined'
  },
  {
    status: 'revising',
    name: '修订中',
    description: '正在修订完善的法律法规',
    color: '#faad14',
    icon: 'EditOutlined'
  },
  {
    status: 'draft',
    name: '草案阶段',
    description: '征求意见或审议阶段',
    color: '#1890ff',
    icon: 'FileAddOutlined'
  },
  {
    status: 'suspended',
    name: '暂停执行',
    description: '因特殊情况暂停执行',
    color: '#722ed1',
    icon: 'PauseCircleOutlined'
  },
  {
    status: 'repealed',
    name: '已废止',
    description: '已被废止的法律法规',
    color: '#8c8c8c',
    icon: 'StopOutlined'
  }
]

// 立法机关
export const LEGISLATIVE_AUTHORITIES = [
  {
    key: 'npc',
    name: '全国人民代表大会',
    abbreviation: '全国人大',
    level: 1,
    description: '国家最高权力机关',
    jurisdiction: ['宪法修正', '基本法律制定']
  },
  {
    key: 'npcsc',
    name: '全国人大常委会',
    abbreviation: '全国人大常委会',
    level: 2,
    description: '全国人大常设机关',
    jurisdiction: ['法律制定修改', '法律解释']
  },
  {
    key: 'state_council',
    name: '国务院',
    abbreviation: '国务院',
    level: 3,
    description: '国家最高行政机关',
    jurisdiction: ['行政法规制定', '部门规章制定']
  },
  {
    key: 'ministries',
    name: '部委',
    abbreviation: '各部委',
    level: 4,
    description: '国务院组成部门',
    jurisdiction: ['部门规章制定', '规范性文件发布']
  }
]

// 获取法律层级信息
export function getLegalLevel(level) {
  return LEGAL_HIERARCHY.find(item => item.level === level)
}

// 获取法律效力状态信息
export function getLegalStatus(status) {
  return LEGAL_STATUS.find(item => item.status === status)
}

// 获取立法机关信息
export function getLegislativeAuthority(key) {
  return LEGISLATIVE_AUTHORITIES.find(item => item.key === key)
}

// 获取法律统计信息
export function getLegalStats() {
  return {
    totalByLevel: LEGAL_HIERARCHY.map(level => ({
      level: level.name,
      count: level.count,
      color: level.color
    })),
    totalCount: LEGAL_HIERARCHY.reduce((sum, level) => sum + level.count, 0),
    byStatus: LEGAL_STATUS.map(status => ({
      status: status.name,
      // 这里可以根据实际数据填充
      count: Math.floor(Math.random() * 50) + 10,
      color: status.color
    }))
  }
}

// 法律检索过滤器
export function filterLegalDocuments(documents, filters) {
  return documents.filter(doc => {
    // 按层级过滤
    if (filters.level && doc.level !== filters.level) {
      return false
    }
    
    // 按状态过滤
    if (filters.status && doc.status !== filters.status) {
      return false
    }
    
    // 按立法机关过滤
    if (filters.authority && doc.authority !== filters.authority) {
      return false
    }
    
    // 按关键词过滤
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase()
      return doc.title.toLowerCase().includes(keyword) ||
             doc.description.toLowerCase().includes(keyword)
    }
    
    // 按生效时间过滤
    if (filters.effectiveDate && filters.effectiveDate.length === 2) {
      const [start, end] = filters.effectiveDate
      const docDate = new Date(doc.effectiveDate)
      return docDate >= start && docDate <= end
    }
    
    return true
  })
} 