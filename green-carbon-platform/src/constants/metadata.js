// 信息资源核心元数据标准 (基于GB/T 21063.4标准)

export const METADATA_SCHEMA = {
  required: ['resourceName', 'resourceSummary', 'classification', 'keywords', 'provider', 'publishDate'],
  properties: {
    resourceName: { 
      type: 'string', 
      maxLength: 100,
      description: '信息资源名称（必选）'
    },
    resourceSummary: { 
      type: 'string', 
      maxLength: 500,
      description: '信息资源摘要（必选）'
    },
    classification: { 
      type: 'string', 
      pattern: '^[A-Z]{2}-\\d{2}-\\d{3}$',
      description: '三级分类代码（必选）'
    },
    keywords: { 
      type: 'array', 
      items: { type: 'string' },
      minItems: 1,
      description: '关键词数组（必选）'
    },
    provider: { 
      type: 'string', 
      maxLength: 100,
      description: '提供方单位（必选）'
    },
    publishDate: { 
      type: 'string', 
      format: 'date',
      description: '发布日期（必选）'
    }
  }
}

// 三级分类体系树结构
export const CLASSIFICATION_TREE = {
  'ZA': {
    name: '综合政务',
    description: '关于政治方面的事务和国家的管理工作',
    subCategories: {
      'ZA-01': {
        name: '双碳工作协调',
        subCategories: {
          'ZA-01-001': '工作机制文件',
          'ZA-01-002': '协调办法',
          'ZA-01-003': '考核评价体系'
        }
      },
      'ZA-02': {
        name: '政策统筹',
        subCategories: {
          'ZA-02-001': '政策规划',
          'ZA-02-002': '实施方案',
          'ZA-02-003': '监督管理'
        }
      }
    }
  },
  'ZB': {
    name: '经济管理',
    description: '宏观经济调控和行业管理',
    subCategories: {
      'ZB-01': {
        name: '绿色金融',
        subCategories: {
          'ZB-01-001': '金融政策',
          'ZB-01-002': '投资指导',
          'ZB-01-003': '风险管控'
        }
      },
      'ZB-02': {
        name: '碳定价机制',
        subCategories: {
          'ZB-02-001': '碳交易政策',
          'ZB-02-002': '碳税政策',
          'ZB-02-003': '价格形成机制'
        }
      }
    }
  },
  'ZC': {
    name: '国土资源与能源',
    description: '土地、矿产、能源资源管理',
    subCategories: {
      'ZC-01': {
        name: '可再生能源',
        subCategories: {
          'ZC-01-001': '发展规划',
          'ZC-01-002': '技术标准',
          'ZC-01-003': '项目管理'
        }
      },
      'ZC-02': {
        name: '能源转型',
        subCategories: {
          'ZC-02-001': '转型路径',
          'ZC-02-002': '政策支持',
          'ZC-02-003': '监测评估'
        }
      }
    }
  },
  'ZD': {
    name: '工业、交通',
    description: '工业、企业、交通运输、邮政及相关领域',
    subCategories: {
      'ZD-01': {
        name: '工业减排',
        subCategories: {
          'ZD-01-001': '行业减排方案',
          'ZD-01-002': '技术改造',
          'ZD-01-003': '能效提升'
        }
      },
      'ZD-02': {
        name: '绿色交通',
        subCategories: {
          'ZD-02-001': '交通减排',
          'ZD-02-002': '新能源车',
          'ZD-02-003': '智慧交通'
        }
      }
    }
  },
  'ZF': {
    name: '城乡建设、环境保护',
    description: '生态环境保护和污染防治',
    subCategories: {
      'ZF-01': {
        name: '碳排放监测',
        subCategories: {
          'ZF-01-001': '监测体系',
          'ZF-01-002': '核查技术',
          'ZF-01-003': '数据管理'
        }
      },
      'ZF-02': {
        name: '绿色建筑',
        subCategories: {
          'ZF-02-001': '建筑标准',
          'ZF-02-002': '节能改造',
          'ZF-02-003': '绿色运营'
        }
      }
    }
  },
  'ZH': {
    name: '财政',
    description: '财政、会计、金融、保险、税务、审计等',
    subCategories: {
      'ZH-01': {
        name: '绿色税收',
        subCategories: {
          'ZH-01-001': '税收政策',
          'ZH-01-002': '优惠措施',
          'ZH-01-003': '征管办法'
        }
      },
      'ZH-02': {
        name: '财政支持',
        subCategories: {
          'ZH-02-001': '专项资金',
          'ZH-02-002': '补贴政策',
          'ZH-02-003': '预算管理'
        }
      }
    }
  },
  'ZP': {
    name: '科技教育',
    description: '科学技术研究和教育培训',
    subCategories: {
      'ZP-01': {
        name: '低碳技术研发',
        subCategories: {
          'ZP-01-001': '项目管理',
          'ZP-01-002': '成果转化',
          'ZP-01-003': '人才培养'
        }
      },
      'ZP-02': {
        name: '科技创新',
        subCategories: {
          'ZP-02-001': '创新平台',
          'ZP-02-002': '技术推广',
          'ZP-02-003': '标准制定'
        }
      }
    }
  }
}

// 元数据验证函数
export function validateMetadata(metadata) {
  const errors = []
  
  // 验证必填字段
  for (const field of METADATA_SCHEMA.required) {
    if (!metadata[field] || metadata[field].toString().trim() === '') {
      errors.push(`${field} 是必填字段`)
    }
  }
  
  // 验证分类代码格式
  if (metadata.classification && !metadata.classification.match(METADATA_SCHEMA.properties.classification.pattern)) {
    errors.push('分类代码格式错误，应为 XX-XX-XXX 格式')
  }
  
  // 验证字符长度
  if (metadata.resourceName && metadata.resourceName.length > METADATA_SCHEMA.properties.resourceName.maxLength) {
    errors.push(`资源名称不能超过 ${METADATA_SCHEMA.properties.resourceName.maxLength} 个字符`)
  }
  
  if (metadata.resourceSummary && metadata.resourceSummary.length > METADATA_SCHEMA.properties.resourceSummary.maxLength) {
    errors.push(`资源摘要不能超过 ${METADATA_SCHEMA.properties.resourceSummary.maxLength} 个字符`)
  }
  
  // 验证关键词
  if (metadata.keywords && (!Array.isArray(metadata.keywords) || metadata.keywords.length === 0)) {
    errors.push('至少需要一个关键词')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

// 获取分类信息
export function getClassificationInfo(code) {
  const [level1, level2, level3] = code.split('-')
  
  if (!CLASSIFICATION_TREE[level1]) return null
  
  const category = CLASSIFICATION_TREE[level1]
  if (!level2) return category
  
  const subCategory = category.subCategories[`${level1}-${level2}`]
  if (!subCategory) return null
  if (!level3) return subCategory
  
  const subSubCategory = subCategory.subCategories[code]
  return subSubCategory || null
} 