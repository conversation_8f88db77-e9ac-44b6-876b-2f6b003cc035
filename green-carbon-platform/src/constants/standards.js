// 标准库分类体系 (基于国家标准体系)

export const STANDARD_CATEGORIES = [
  {
    type: 'national_standard',
    level: '国家标准(GB)',
    domain: '术语定义、分类指南',
    example: 'GB/T 32150-2015 工业企业温室气体排放核算和报告通则',
    status: '现行有效',
    count: 456,
    color: '#f5222d',
    icon: 'CrownOutlined'
  },
  {
    type: 'industry_standard',
    level: '行业标准(HY/T)',
    domain: '各行业温室气体核算方法',
    example: 'HY/T 0273-2019 海洋碳汇核算方法',
    status: '现行有效',
    count: 234,
    color: '#fa541c',
    icon: 'BuildOutlined'
  },
  {
    type: 'international_standard',
    level: '国际标准(ISO)',
    domain: '环境管理体系、碳足迹',
    example: 'ISO 14064系列 温室气体核算与验证',
    status: '国际适用',
    count: 178,
    color: '#52c41a',
    icon: 'GlobalOutlined'
  },
  {
    type: 'group_standard',
    level: '团体标准(T)',
    domain: '低碳技术评价规范',
    example: 'T/CECA-G 0021-2020 绿色工厂评价通则',
    status: '现行有效',
    count: 312,
    color: '#1890ff',
    icon: 'TeamOutlined'
  }
]

// 标准状态分类
export const STANDARD_STATUS = [
  { status: 'current', name: '现行有效', color: '#52c41a' },
  { status: 'draft', name: '征求意见', color: '#faad14' },
  { status: 'upcoming', name: '即将实施', color: '#1890ff' },
  { status: 'revising', name: '修订中', color: '#722ed1' },
  { status: 'obsolete', name: '已废止', color: '#8c8c8c' }
]

// 技术领域分类
export const TECHNICAL_DOMAINS = [
  { domain: 'carbon_accounting', name: '碳核算', count: 89, color: '#52c41a' },
  { domain: 'energy_efficiency', name: '能效管理', count: 156, color: '#faad14' },
  { domain: 'green_building', name: '绿色建筑', count: 123, color: '#13c2c2' },
  { domain: 'clean_energy', name: '清洁能源', count: 198, color: '#722ed1' },
  { domain: 'environmental_management', name: '环境管理', count: 167, color: '#eb2f96' },
  { domain: 'carbon_trading', name: '碳交易', count: 67, color: '#f5222d' }
]

// 标准制定机构
export const STANDARD_ORGANIZATIONS = [
  {
    key: 'sac',
    name: '国家标准化管理委员会',
    abbreviation: 'SAC',
    level: 'national',
    description: '国家标准制定和管理机构',
    website: 'http://www.sac.gov.cn',
    standardTypes: ['GB', 'GB/T']
  },
  {
    key: 'iso',
    name: '国际标准化组织',
    abbreviation: 'ISO',
    level: 'international',
    description: '国际标准制定组织',
    website: 'https://www.iso.org',
    standardTypes: ['ISO']
  },
  {
    key: 'iec',
    name: '国际电工委员会',
    abbreviation: 'IEC',
    level: 'international',
    description: '电工电子领域国际标准',
    website: 'https://www.iec.ch',
    standardTypes: ['IEC']
  },
  {
    key: 'ceca',
    name: '中国节能协会',
    abbreviation: 'CECA',
    level: 'group',
    description: '节能领域团体标准制定',
    website: 'http://www.ceca.org.cn',
    standardTypes: ['T/CECA']
  }
]

// 获取标准分类信息
export function getStandardCategory(type) {
  return STANDARD_CATEGORIES.find(cat => cat.type === type)
}

// 获取技术领域信息
export function getTechnicalDomain(domain) {
  return TECHNICAL_DOMAINS.find(item => item.domain === domain)
}

// 获取标准制定机构信息
export function getStandardOrganization(key) {
  return STANDARD_ORGANIZATIONS.find(org => org.key === key)
}

// 根据标准编号解析标准信息
export function parseStandardCode(code) {
  const patterns = [
    { regex: /^GB\/T?\s*(\d+)-(\d+)/, type: 'national_standard', prefix: 'GB' },
    { regex: /^HY\/T?\s*(\d+)-(\d+)/, type: 'industry_standard', prefix: 'HY' },
    { regex: /^ISO\s*(\d+)/, type: 'international_standard', prefix: 'ISO' },
    { regex: /^T\/([A-Z]+)-?([A-Z]*)\s*(\d+)-(\d+)/, type: 'group_standard', prefix: 'T' }
  ]
  
  for (const pattern of patterns) {
    const match = code.match(pattern.regex)
    if (match) {
      return {
        type: pattern.type,
        prefix: pattern.prefix,
        category: getStandardCategory(pattern.type),
        isValid: true
      }
    }
  }
  
  return {
    isValid: false,
    error: '无法识别的标准编号格式'
  }
}

// 获取标准统计信息
export function getStandardStats() {
  return {
    totalByCategory: STANDARD_CATEGORIES.map(cat => ({
      category: cat.level,
      count: cat.count,
      color: cat.color
    })),
    totalCount: STANDARD_CATEGORIES.reduce((sum, cat) => sum + cat.count, 0),
    byDomain: TECHNICAL_DOMAINS.map(domain => ({
      domain: domain.name,
      count: domain.count,
      color: domain.color
    })),
    byStatus: STANDARD_STATUS.map(status => ({
      status: status.name,
      // 这里可以根据实际数据填充
      count: Math.floor(Math.random() * 100) + 20,
      color: status.color
    }))
  }
}

// 标准检索过滤器
export function filterStandards(standards, filters) {
  return standards.filter(standard => {
    // 按分类过滤
    if (filters.category && standard.category !== filters.category) {
      return false
    }
    
    // 按状态过滤
    if (filters.status && standard.status !== filters.status) {
      return false
    }
    
    // 按技术领域过滤
    if (filters.domain && standard.domain !== filters.domain) {
      return false
    }
    
    // 按标准编号过滤
    if (filters.code && !standard.code.toLowerCase().includes(filters.code.toLowerCase())) {
      return false
    }
    
    // 按关键词过滤
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase()
      return standard.title.toLowerCase().includes(keyword) ||
             standard.description.toLowerCase().includes(keyword) ||
             standard.code.toLowerCase().includes(keyword)
    }
    
    // 按发布时间过滤
    if (filters.publishDate && filters.publishDate.length === 2) {
      const [start, end] = filters.publishDate
      const stdDate = new Date(standard.publishDate)
      return stdDate >= start && stdDate <= end
    }
    
    return true
  })
} 