// ZA-ZP政策分类体系 (基于GB/T 21063.4-2007 政务信息资源分类)

export const POLICY_CATEGORIES = [
  {
    code: 'ZA',
    name: '综合政务',
    description: '关于政治方面的事务和国家的管理工作',
    icon: 'TeamOutlined',
    examples: ['"双碳"工作协调机制文件', '部门协调办法', '考核评价体系'],
    color: '#1890ff',
    subCategories: [
      { code: 'ZA01', name: '工作机制', count: 156, description: '双碳工作协调机制建设' },
      { code: 'ZA02', name: '协调办法', count: 89, description: '跨部门协调管理办法' },
      { code: 'ZA03', name: '考核评价', count: 67, description: '双碳工作考核评价体系' },
      { code: 'ZA04', name: '监督管理', count: 43, description: '双碳工作监督管理制度' }
    ]
  },
  {
    code: 'ZB',
    name: '经济管理',
    description: '宏观经济调控和行业管理',
    icon: 'LineChartOutlined',
    examples: ['绿色金融政策', '碳定价机制', '绿色投资指导'],
    color: '#52c41a',
    subCategories: [
      { code: 'ZB01', name: '绿色金融', count: 234, description: '绿色金融政策和制度' },
      { code: 'ZB02', name: '碳定价机制', count: 187, description: '碳交易和碳税政策' },
      { code: 'ZB03', name: '产业政策', count: 156, description: '绿色产业发展政策' },
      { code: 'ZB04', name: '投资指导', count: 98, description: '绿色投资指导目录' },
      { code: 'ZB05', name: '市场机制', count: 76, description: '绿色市场机制建设' }
    ]
  },
  {
    code: 'ZC',
    name: '国土资源与能源',
    description: '土地、矿产、能源资源管理',
    icon: 'ThunderboltOutlined',
    examples: ['可再生能源发展规划', '能源结构调整', '资源节约利用'],
    color: '#faad14',
    subCategories: [
      { code: 'ZC01', name: '可再生能源', count: 298, description: '风光水核等可再生能源' },
      { code: 'ZC02', name: '传统能源', count: 167, description: '煤炭石油天然气清洁利用' },
      { code: 'ZC03', name: '能源转型', count: 134, description: '能源结构调整和转型' },
      { code: 'ZC04', name: '资源节约', count: 112, description: '资源节约和循环利用' },
      { code: 'ZC05', name: '国土空间', count: 89, description: '国土空间规划和管理' }
    ]
  },
  {
    code: 'ZD',
    name: '工业、交通',
    description: '工业、企业、交通运输、邮政及相关领域',
    icon: 'CarOutlined',
    examples: ['工业减排方案', '绿色交通发展', '物流减排'],
    color: '#722ed1',
    subCategories: [
      { code: 'ZD01', name: '工业减排', count: 345, description: '重点行业减排技术方案' },
      { code: 'ZD02', name: '绿色制造', count: 278, description: '绿色工厂和绿色园区' },
      { code: 'ZD03', name: '交通减排', count: 234, description: '交通运输减排政策' },
      { code: 'ZD04', name: '新能源车', count: 189, description: '新能源汽车推广应用' },
      { code: 'ZD05', name: '智慧交通', count: 156, description: '智慧交通系统建设' }
    ]
  },
  {
    code: 'ZF',
    name: '城乡建设、环境保护',
    description: '生态环境保护和污染防治',
    icon: 'BuildOutlined',
    examples: ['碳排放监测核查技术规范', '绿色建筑标准', '生态保护'],
    color: '#13c2c2',
    subCategories: [
      { code: 'ZF01', name: '碳排放监测', count: 267, description: '碳排放监测、核查技术' },
      { code: 'ZF02', name: '绿色建筑', count: 234, description: '绿色建筑设计和运营' },
      { code: 'ZF03', name: '生态保护', count: 198, description: '生态环境保护修复' },
      { code: 'ZF04', name: '污染防治', count: 167, description: '大气水土壤污染防治' },
      { code: 'ZF05', name: '城市规划', count: 123, description: '绿色城市和海绵城市' }
    ]
  },
  {
    code: 'ZH',
    name: '财政',
    description: '财政、会计、金融、保险、税务、审计等',
    icon: 'DollarOutlined',
    examples: ['绿色税收政策', '财政支持政策', '绿色采购'],
    color: '#f5222d',
    subCategories: [
      { code: 'ZH01', name: '绿色税收', count: 189, description: '环保税、碳税等绿色税收' },
      { code: 'ZH02', name: '财政支持', count: 156, description: '绿色发展专项资金' },
      { code: 'ZH03', name: '绿色采购', count: 123, description: '政府绿色采购政策' },
      { code: 'ZH04', name: '会计核算', count: 98, description: '环境会计和碳核算' },
      { code: 'ZH05', name: '审计监督', count: 76, description: '绿色发展审计监督' }
    ]
  },
  {
    code: 'ZP',
    name: '科技教育',
    description: '科学技术研究和教育培训',
    icon: 'ExperimentOutlined',
    examples: ['低碳技术研发项目管理', '绿色技术推广', '人才培养'],
    color: '#eb2f96',
    subCategories: [
      { code: 'ZP01', name: '技术研发', count: 298, description: '低碳技术研发和创新' },
      { code: 'ZP02', name: '成果转化', count: 234, description: '科技成果转化应用' },
      { code: 'ZP03', name: '人才培养', count: 189, description: '双碳领域人才培养' },
      { code: 'ZP04', name: '标准制定', count: 156, description: '技术标准制定修订' },
      { code: 'ZP05', name: '国际合作', count: 123, description: '绿色技术国际合作' }
    ]
  }
]

// 政策工作流程
export const POLICY_WORKFLOW = [
  {
    step: 1,
    name: '政策解读',
    description: '深度解读政策内容和要求',
    icon: 'FileTextOutlined'
  },
  {
    step: 2,
    name: '企业行动指南',
    description: '为企业提供具体行动指导',
    icon: 'SolutionOutlined'
  },
  {
    step: 3,
    name: '服务对接',
    description: '对接相关服务和资源',
    icon: 'LinkOutlined'
  }
]

// 政策级别分类
export const POLICY_LEVELS = [
  { key: 'national', name: '国家级', description: '国务院及部委发布', color: '#f5222d' },
  { key: 'ministerial', name: '部委级', description: '各部委发布', color: '#fa541c' },
  { key: 'provincial', name: '省级', description: '省级政府发布', color: '#faad14' },
  { key: 'municipal', name: '市级', description: '市级政府发布', color: '#52c41a' }
]

// 政策状态
export const POLICY_STATUS = [
  { key: 'current', name: '现行有效', description: '当前有效的政策', color: '#52c41a' },
  { key: 'draft', name: '征求意见', description: '征求意见稿阶段', color: '#faad14' },
  { key: 'revising', name: '修订中', description: '政策修订完善中', color: '#1890ff' },
  { key: 'replaced', name: '已被替代', description: '被新政策替代', color: '#8c8c8c' },
  { key: 'expired', name: '已失效', description: '政策已失效', color: '#f5222d' }
]

// 获取政策分类信息
export function getPolicyCategory(code) {
  return POLICY_CATEGORIES.find(cat => cat.code === code)
}

// 获取政策子分类信息
export function getPolicySubCategory(categoryCode, subCode) {
  const category = getPolicyCategory(categoryCode)
  if (!category) return null
  return category.subCategories.find(sub => sub.code === subCode)
}

// 根据分类代码获取统计信息
export function getPolicyStats() {
  return POLICY_CATEGORIES.map(category => ({
    code: category.code,
    name: category.name,
    total: category.subCategories.reduce((sum, sub) => sum + sub.count, 0),
    subCategories: category.subCategories.length
  }))
}

// 政策搜索过滤器
export function filterPolicies(policies, filters) {
  return policies.filter(policy => {
    // 按分类过滤
    if (filters.category && policy.category !== filters.category) {
      return false
    }
    
    // 按级别过滤
    if (filters.level && policy.level !== filters.level) {
      return false
    }
    
    // 按状态过滤
    if (filters.status && policy.status !== filters.status) {
      return false
    }
    
    // 按关键词过滤
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase()
      return policy.title.toLowerCase().includes(keyword) ||
             policy.summary.toLowerCase().includes(keyword) ||
             policy.keywords.some(k => k.toLowerCase().includes(keyword))
    }
    
    // 按时间范围过滤
    if (filters.dateRange && filters.dateRange.length === 2) {
      const [start, end] = filters.dateRange
      const policyDate = new Date(policy.publishDate)
      return policyDate >= start && policyDate <= end
    }
    
    return true
  })
} 