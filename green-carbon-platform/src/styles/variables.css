/* 国家绿色低碳全产业链服务平台 - CSS变量 */

:root {
  /* 基础颜色 */
  --primary-color: #2c5f2d;
  --primary-light: #689a3d;
  --primary-lighter: #97bc62;
  --secondary-color: #f0f4f0;
  --accent-color: #52c41a;

  /* 文本颜色 */
  --text-primary: #1a1a1a;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-light: #cccccc;

  /* 背景颜色 */
  --background-base: #ffffff;
  --background-light: #f8f9fa;
  --background-lighter: #f0f4f0;
  --background-dark: #2c5f2d;

  /* 边框颜色 */
  --border-color: #d9d9d9;
  --border-color-light: #f0f0f0;
  --border-color-lighter: #f5f5f5;

  /* 状态颜色 */
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --info-color: #1890ff;

  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, #2c5f2d 0%, #689a3d 50%, #97bc62 100%);
  --gradient-background: linear-gradient(135deg, #f8faf8 0%, #f0f4f0 100%);
  --gradient-card: linear-gradient(135deg, #ffffff 0%, #f8fafa 100%);
  --gradient-light: linear-gradient(135deg, #f0f4f0 0%, #e8f0e8 100%);

  /* 阴影 */
  --box-shadow-light: 0 4px 12px rgba(0, 0, 0, 0.04);
  --box-shadow-medium: 0 8px 24px rgba(44, 95, 45, 0.12);
  --box-shadow-heavy: 0 12px 32px rgba(44, 95, 45, 0.15);
  --box-shadow-hover: 0 8px 24px rgba(44, 95, 45, 0.15);

  /* 圆角 */
  --border-radius-small: 4px;
  --border-radius-base: 8px;
  --border-radius-large: 12px;
  --border-radius-xlarge: 16px;
  --border-radius-round: 50%;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-base: 12px;
  --spacing-md: 16px;
  --spacing-lg: 20px;
  --spacing-xl: 24px;
  --spacing-xxl: 32px;

  /* 字体大小 */
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-xxl: 20px;
  --font-size-xxxl: 24px;

  /* 字体粗细 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-base: 1.4;
  --line-height-loose: 1.6;

  /* 过渡动画 */
  --transition-fast: all 0.2s ease;
  --transition-base: all 0.3s ease;
  --transition-slow: all 0.5s ease;

  /* 布局尺寸 */
  --header-height: 64px;
  --footer-height: 50px;
  --sidebar-width: 200px;
  --sidebar-collapsed-width: 80px;

  /* Z-Index */
  --z-index-header: 100;
  --z-index-sidebar: 10;
  --z-index-modal: 1000;
  --z-index-tooltip: 1500;

  /* 响应式断点 */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;
  --breakpoint-md: 992px;
  --breakpoint-lg: 1200px;
  --breakpoint-xl: 1600px;
}

/* 全局重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

::-webkit-scrollbar-corner {
  background: #f5f5f5;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-base); }
.mb-4 { margin-bottom: var(--spacing-md); }
.mb-5 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-base); }
.mt-4 { margin-top: var(--spacing-md); }
.mt-5 { margin-top: var(--spacing-lg); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-base); }
.p-4 { padding: var(--spacing-md); }
.p-5 { padding: var(--spacing-lg); }

.d-flex { display: flex; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }
.flex-wrap { flex-wrap: wrap; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }
.mw-100 { max-width: 100%; }
.mh-100 { max-height: 100%; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-auto { overflow-y: auto; }

.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }

.border-radius-sm { border-radius: var(--border-radius-small); }
.border-radius { border-radius: var(--border-radius-base); }
.border-radius-lg { border-radius: var(--border-radius-large); }

.shadow-sm { box-shadow: var(--box-shadow-light); }
.shadow { box-shadow: var(--box-shadow-medium); }
.shadow-lg { box-shadow: var(--box-shadow-heavy); }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--text-secondary); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-light { background-color: var(--background-light); }
.bg-white { background-color: var(--background-base); }

.cursor-pointer { cursor: pointer; }
.cursor-default { cursor: default; }

.user-select-none { user-select: none; }

/* 防止文本溢出 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-break {
  word-wrap: break-word;
  word-break: break-all;
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .d-sm-none { display: none; }
  .d-sm-block { display: block; }
  .d-sm-flex { display: flex; }
}

@media (max-width: 480px) {
  .d-xs-none { display: none; }
  .d-xs-block { display: block; }
  .d-xs-flex { display: flex; }
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Ant Design 定制 */
.ant-layout {
  background: transparent;
}

.ant-layout-header {
  padding: 0;
  height: var(--header-height);
  line-height: var(--header-height);
}

.ant-layout-footer {
  padding: 12px 24px;
  height: var(--footer-height);
  line-height: 26px;
}

.ant-layout-sider {
  background: var(--background-base);
}

.ant-menu {
  background: transparent;
  border-right: none;
}

.ant-menu-item {
  margin: 4px 8px;
  border-radius: var(--border-radius-base);
  height: 36px;
  line-height: 36px;
}

.ant-menu-item:hover {
  background: rgba(44, 95, 45, 0.1);
  color: var(--primary-color);
}

.ant-menu-item-selected {
  background: var(--gradient-primary);
  color: white;
}

.ant-card {
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
  border: 1px solid var(--border-color-light);
}

.ant-card:hover {
  box-shadow: var(--box-shadow-medium);
}

.ant-btn {
  border-radius: var(--border-radius-base);
  transition: var(--transition-base);
}

.ant-btn-primary {
  background: var(--gradient-primary);
  border: none;
}

.ant-btn-primary:hover {
  background: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-medium);
}

.ant-input {
  border-radius: var(--border-radius-base);
  border-color: var(--border-color);
}

.ant-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(44, 95, 45, 0.2);
}

.ant-select {
  border-radius: var(--border-radius-base);
}

.ant-table {
  border-radius: var(--border-radius-base);
}

.ant-pagination {
  text-align: center;
  margin-top: var(--spacing-lg);
}

/* 修复Ant Design栅格负边距问题 */
.ant-row {
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
}

.ant-col {
  padding-left: 8px !important;
  padding-right: 8px !important;
  max-width: 100% !important;
  min-width: 0 !important;
}

/* 防止元素溢出 */
* {
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.ant-row,
.ant-col {
  min-width: 0;
} 