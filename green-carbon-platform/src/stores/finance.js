import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { FINANCE_CATEGORIES } from '@/constants/finance'

export const useFinanceStore = defineStore('finance', () => {
  // 状态
  const financeCategories = ref(FINANCE_CATEGORIES)
  const productList = ref([])
  const applicationList = ref([])
  const currentProduct = ref(null)
  const loading = ref(false)
  const selectedType = ref('')
  const searchQuery = ref('')

  // 计算属性
  const totalProducts = computed(() => productList.value.length)
  const totalApplications = computed(() => applicationList.value.length)

  const productsByType = computed(() => {
    const result = {}
    financeCategories.value.forEach(category => {
      result[category.type] = productList.value.filter(p => p.type === category.type)
    })
    return result
  })

  const filteredProducts = computed(() => {
    let filtered = productList.value

    // 按类型筛选
    if (selectedType.value) {
      filtered = filtered.filter(p => p.type === selectedType.value)
    }

    // 按搜索关键词筛选
    if (searchQuery.value.trim()) {
      const keyword = searchQuery.value.toLowerCase()
      filtered = filtered.filter(p => 
        p.name.toLowerCase().includes(keyword) ||
        p.description.toLowerCase().includes(keyword) ||
        p.tags.some(t => t.toLowerCase().includes(keyword))
      )
    }

    return filtered
  })

  const applicationsByStatus = computed(() => {
    const result = {
      pending: [],
      approved: [],
      rejected: [],
      processing: []
    }
    
    applicationList.value.forEach(app => {
      if (result[app.status]) {
        result[app.status].push(app)
      }
    })
    
    return result
  })

  // 获取产品统计
  const getProductStatistics = computed(() => {
    const stats = {
      totalProducts: totalProducts.value,
      byType: {},
      byRegulator: {},
      applicationStats: {
        total: totalApplications.value,
        pending: applicationsByStatus.value.pending.length,
        approved: applicationsByStatus.value.approved.length,
        rejected: applicationsByStatus.value.rejected.length,
        processing: applicationsByStatus.value.processing.length
      }
    }

    // 按类型统计
    financeCategories.value.forEach(category => {
      stats.byType[category.type] = getProductsByType(category.type).length
    })

    // 按监管机构统计
    const regulatorCounts = {}
    productList.value.forEach(product => {
      product.regulators?.forEach(regulator => {
        regulatorCounts[regulator] = (regulatorCounts[regulator] || 0) + 1
      })
    })
    stats.byRegulator = regulatorCounts

    return stats
  })

  // 产品管理方法
  const addProduct = (product) => {
    const newProduct = {
      ...product,
      id: Date.now().toString(),
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      status: 'active',
      applicationCount: 0,
      approvalRate: 0
    }
    productList.value.push(newProduct)
    return { success: true, product: newProduct }
  }

  const updateProduct = (id, updates) => {
    const index = productList.value.findIndex(p => p.id === id)
    if (index !== -1) {
      productList.value[index] = {
        ...productList.value[index],
        ...updates,
        updateTime: new Date().toISOString()
      }
      return { success: true, product: productList.value[index] }
    }
    return { success: false, errors: ['产品不存在'] }
  }

  const deleteProduct = (id) => {
    const index = productList.value.findIndex(p => p.id === id)
    if (index !== -1) {
      productList.value.splice(index, 1)
      return { success: true }
    }
    return { success: false, errors: ['产品不存在'] }
  }

  // 获取指定类型的产品
  const getProductsByType = (type) => {
    return productList.value.filter(p => p.type === type)
  }

  // 获取推荐产品
  const getRecommendedProducts = (requirements) => {
    // 根据需求匹配产品
    const {
      companyType = '',
      projectType = '',
      fundingAmount = 0,
      riskLevel = 'medium',
      urgency = 'normal'
    } = requirements

    let scored = productList.value.map(product => {
      let score = 0

      // 根据公司类型匹配
      if (product.targets?.some(target => 
        target.toLowerCase().includes(companyType.toLowerCase())
      )) {
        score += 30
      }

      // 根据项目类型匹配
      if (product.applicableProjects?.some(project => 
        project.toLowerCase().includes(projectType.toLowerCase())
      )) {
        score += 25
      }

      // 根据资金额度匹配
      if (product.minAmount && product.maxAmount) {
        if (fundingAmount >= product.minAmount && fundingAmount <= product.maxAmount) {
          score += 20
        }
      }

      // 根据风险等级匹配
      if (product.riskLevel === riskLevel) {
        score += 15
      }

      // 根据处理时效匹配
      if (urgency === 'urgent' && product.processingTime <= 7) {
        score += 10
      }

      return { ...product, matchScore: score }
    })

    return scored
      .filter(p => p.matchScore > 0)
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, 10)
  }

  // 申请管理方法
  const submitApplication = (application) => {
    const newApplication = {
      ...application,
      id: Date.now().toString(),
      submitTime: new Date().toISOString(),
      status: 'pending',
      progress: [
        {
          step: '申请提交',
          time: new Date().toISOString(),
          status: 'completed'
        }
      ]
    }
    applicationList.value.push(newApplication)
    
    // 更新产品申请计数
    const productIndex = productList.value.findIndex(p => p.id === application.productId)
    if (productIndex !== -1) {
      productList.value[productIndex].applicationCount += 1
    }

    return { success: true, application: newApplication }
  }

  const updateApplicationStatus = (id, status, note = '') => {
    const index = applicationList.value.findIndex(a => a.id === id)
    if (index !== -1) {
      applicationList.value[index].status = status
      applicationList.value[index].progress.push({
        step: getStatusText(status),
        time: new Date().toISOString(),
        status: 'completed',
        note
      })
      return { success: true, application: applicationList.value[index] }
    }
    return { success: false, errors: ['申请不存在'] }
  }

  const getStatusText = (status) => {
    const statusMap = {
      pending: '申请提交',
      processing: '审核中',
      approved: '审核通过',
      rejected: '审核拒绝'
    }
    return statusMap[status] || status
  }

  // 搜索和筛选
  const searchProducts = (query) => {
    searchQuery.value = query
  }

  const setSelectedType = (type) => {
    selectedType.value = type
  }

  // 获取产品详情
  const getProductById = (id) => {
    return productList.value.find(p => p.id === id)
  }

  // 获取申请历史
  const getApplicationsByUser = (userId) => {
    return applicationList.value.filter(a => a.userId === userId)
  }

  // 获取产品相关申请
  const getApplicationsByProduct = (productId) => {
    return applicationList.value.filter(a => a.productId === productId)
  }

  // 重置状态
  const resetState = () => {
    productList.value = []
    applicationList.value = []
    currentProduct.value = null
    loading.value = false
    selectedType.value = ''
    searchQuery.value = ''
  }

  return {
    // 状态
    financeCategories,
    productList,
    applicationList,
    currentProduct,
    loading,
    selectedType,
    searchQuery,

    // 计算属性
    totalProducts,
    totalApplications,
    productsByType,
    filteredProducts,
    applicationsByStatus,
    getProductStatistics,

    // 方法
    addProduct,
    updateProduct,
    deleteProduct,
    getProductsByType,
    getRecommendedProducts,
    submitApplication,
    updateApplicationStatus,
    searchProducts,
    setSelectedType,
    getProductById,
    getApplicationsByUser,
    getApplicationsByProduct,
    resetState
  }
}) 