import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { METADATA_SCHEMA, CLASSIFICATION_TREE } from '@/constants/metadata'

export const useMetadataStore = defineStore('metadata', () => {
  // 状态
  const resourceList = ref([])
  const classificationTree = ref(CLASSIFICATION_TREE)
  const metadataSchema = ref(METADATA_SCHEMA)
  const currentResource = ref(null)
  const loading = ref(false)

  // 计算属性
  const totalResources = computed(() => resourceList.value.length)
  const resourcesByCategory = computed(() => {
    const categories = {}
    resourceList.value.forEach(resource => {
      const category = resource.classification?.substring(0, 2) || 'Unknown'
      if (!categories[category]) {
        categories[category] = []
      }
      categories[category].push(resource)
    })
    return categories
  })

  // 验证元数据
  const validateMetadata = (metadata) => {
    const errors = []
    
    // 验证必填字段
    metadataSchema.value.required.forEach(field => {
      if (!metadata[field] || metadata[field].toString().trim() === '') {
        errors.push(`${field} 是必填字段`)
      }
    })
    
    // 验证分类代码格式
    if (metadata.classification && !metadata.classification.match(/^[A-Z]{2}-\d{2}-\d{3}$/)) {
      errors.push('分类代码格式不正确，应为：ZA-01-001')
    }
    
    // 验证关键词
    if (metadata.keywords && !Array.isArray(metadata.keywords)) {
      errors.push('关键词必须是数组格式')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // 添加资源
  const addResource = (resource) => {
    const validation = validateMetadata(resource)
    if (validation.isValid) {
      const newResource = {
        ...resource,
        id: Date.now().toString(),
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
      resourceList.value.push(newResource)
      return { success: true, resource: newResource }
    }
    return { success: false, errors: validation.errors }
  }

  // 更新资源
  const updateResource = (id, updates) => {
    const index = resourceList.value.findIndex(r => r.id === id)
    if (index !== -1) {
      const updatedResource = {
        ...resourceList.value[index],
        ...updates,
        updateTime: new Date().toISOString()
      }
      
      const validation = validateMetadata(updatedResource)
      if (validation.isValid) {
        resourceList.value[index] = updatedResource
        return { success: true, resource: updatedResource }
      }
      return { success: false, errors: validation.errors }
    }
    return { success: false, errors: ['资源不存在'] }
  }

  // 删除资源
  const deleteResource = (id) => {
    const index = resourceList.value.findIndex(r => r.id === id)
    if (index !== -1) {
      resourceList.value.splice(index, 1)
      return { success: true }
    }
    return { success: false, errors: ['资源不存在'] }
  }

  // 搜索资源
  const searchResources = (query) => {
    if (!query.trim()) return resourceList.value
    
    const keyword = query.toLowerCase()
    return resourceList.value.filter(resource => 
      resource.resourceName.toLowerCase().includes(keyword) ||
      resource.resourceSummary.toLowerCase().includes(keyword) ||
      resource.keywords.some(k => k.toLowerCase().includes(keyword)) ||
      resource.provider.toLowerCase().includes(keyword)
    )
  }

  // 按分类筛选
  const getResourcesByClassification = (classification) => {
    return resourceList.value.filter(r => 
      r.classification.startsWith(classification)
    )
  }

  // 重置状态
  const resetState = () => {
    resourceList.value = []
    currentResource.value = null
    loading.value = false
  }

  return {
    // 状态
    resourceList,
    classificationTree,
    metadataSchema,
    currentResource,
    loading,
    
    // 计算属性
    totalResources,
    resourcesByCategory,
    
    // 方法
    validateMetadata,
    addResource,
    updateResource,
    deleteResource,
    searchResources,
    getResourcesByClassification,
    resetState
  }
}) 