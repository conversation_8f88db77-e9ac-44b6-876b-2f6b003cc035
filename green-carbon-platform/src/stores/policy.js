import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { POLICY_CATEGORIES, LEGAL_HIERARCHY } from '@/constants/policies'
import { STANDARD_CATEGORIES } from '@/constants/standards'

export const usePolicyStore = defineStore('policy', () => {
  // 状态
  const policyCategories = ref(POLICY_CATEGORIES)
  const legalHierarchy = ref(LEGAL_HIERARCHY)
  const standardCategories = ref(STANDARD_CATEGORIES)
  const policyList = ref([])
  const legalList = ref([])
  const standardList = ref([])
  const currentPolicy = ref(null)
  const loading = ref(false)
  const searchQuery = ref('')
  const selectedCategory = ref('')
  const selectedLevel = ref('')

  // 计算属性
  const totalPolicies = computed(() => policyList.value.length)
  const totalLegalDocs = computed(() => legalList.value.length)
  const totalStandards = computed(() => standardList.value.length)

  const filteredPolicies = computed(() => {
    let filtered = policyList.value

    // 按分类筛选
    if (selectedCategory.value) {
      filtered = filtered.filter(p => p.category === selectedCategory.value)
    }

    // 按搜索关键词筛选
    if (searchQuery.value.trim()) {
      const keyword = searchQuery.value.toLowerCase()
      filtered = filtered.filter(p => 
        p.title.toLowerCase().includes(keyword) ||
        p.summary.toLowerCase().includes(keyword) ||
        p.keywords.some(k => k.toLowerCase().includes(keyword))
      )
    }

    return filtered
  })

  const policiesByCategory = computed(() => {
    const result = {}
    policyCategories.value.forEach(category => {
      result[category.code] = policyList.value.filter(p => p.category === category.code)
    })
    return result
  })

  const legalDocsByLevel = computed(() => {
    const result = {}
    legalHierarchy.value.forEach(level => {
      result[level.level] = legalList.value.filter(l => l.level === level.level)
    })
    return result
  })

  const standardsByType = computed(() => {
    const result = {}
    standardCategories.value.forEach(type => {
      result[type.type] = standardList.value.filter(s => s.type === type.type)
    })
    return result
  })

  // 政策管理方法
  const addPolicy = (policy) => {
    const newPolicy = {
      ...policy,
      id: Date.now().toString(),
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      status: '有效',
      viewCount: 0
    }
    policyList.value.push(newPolicy)
    return { success: true, policy: newPolicy }
  }

  const updatePolicy = (id, updates) => {
    const index = policyList.value.findIndex(p => p.id === id)
    if (index !== -1) {
      policyList.value[index] = {
        ...policyList.value[index],
        ...updates,
        updateTime: new Date().toISOString()
      }
      return { success: true, policy: policyList.value[index] }
    }
    return { success: false, errors: ['政策不存在'] }
  }

  const deletePolicy = (id) => {
    const index = policyList.value.findIndex(p => p.id === id)
    if (index !== -1) {
      policyList.value.splice(index, 1)
      return { success: true }
    }
    return { success: false, errors: ['政策不存在'] }
  }

  // 获取指定分类的政策
  const getPoliciesByCategory = (categoryCode) => {
    return policyList.value.filter(p => p.category === categoryCode)
  }

  // 获取相关政策推荐
  const getRelatedPolicies = (policyId, limit = 5) => {
    const currentPolicy = policyList.value.find(p => p.id === policyId)
    if (!currentPolicy) return []

    return policyList.value
      .filter(p => p.id !== policyId && p.category === currentPolicy.category)
      .slice(0, limit)
  }

  // 法律法规管理方法
  const addLegalDoc = (doc) => {
    const newDoc = {
      ...doc,
      id: Date.now().toString(),
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
    legalList.value.push(newDoc)
    return { success: true, doc: newDoc }
  }

  const getLegalDocsByLevel = (level) => {
    return legalList.value.filter(l => l.level === level)
  }

  // 标准管理方法
  const addStandard = (standard) => {
    const newStandard = {
      ...standard,
      id: Date.now().toString(),
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
    standardList.value.push(newStandard)
    return { success: true, standard: newStandard }
  }

  const getStandardsByType = (type) => {
    return standardList.value.filter(s => s.type === type)
  }

  // 搜索功能
  const searchPolicies = (query) => {
    searchQuery.value = query
  }

  const setSelectedCategory = (category) => {
    selectedCategory.value = category
  }

  const setSelectedLevel = (level) => {
    selectedLevel.value = level
  }

  // 获取统计数据
  const getStatistics = () => {
    const stats = {
      policies: {
        total: totalPolicies.value,
        byCategory: {}
      },
      legal: {
        total: totalLegalDocs.value,
        byLevel: {}
      },
      standards: {
        total: totalStandards.value,
        byType: {}
      }
    }

    // 统计各分类政策数量
    policyCategories.value.forEach(category => {
      stats.policies.byCategory[category.code] = getPoliciesByCategory(category.code).length
    })

    // 统计各层级法律文件数量
    legalHierarchy.value.forEach(level => {
      stats.legal.byLevel[level.level] = getLegalDocsByLevel(level.level).length
    })

    // 统计各类型标准数量
    standardCategories.value.forEach(type => {
      stats.standards.byType[type.type] = getStandardsByType(type.type).length
    })

    return stats
  }

  // 重置状态
  const resetState = () => {
    policyList.value = []
    legalList.value = []
    standardList.value = []
    currentPolicy.value = null
    loading.value = false
    searchQuery.value = ''
    selectedCategory.value = ''
    selectedLevel.value = ''
  }

  return {
    // 状态
    policyCategories,
    legalHierarchy,
    standardCategories,
    policyList,
    legalList,
    standardList,
    currentPolicy,
    loading,
    searchQuery,
    selectedCategory,
    selectedLevel,

    // 计算属性
    totalPolicies,
    totalLegalDocs,
    totalStandards,
    filteredPolicies,
    policiesByCategory,
    legalDocsByLevel,
    standardsByType,

    // 方法
    addPolicy,
    updatePolicy,
    deletePolicy,
    getPoliciesByCategory,
    getRelatedPolicies,
    addLegalDoc,
    getLegalDocsByLevel,
    addStandard,
    getStandardsByType,
    searchPolicies,
    setSelectedCategory,
    setSelectedLevel,
    getStatistics,
    resetState
  }
}) 