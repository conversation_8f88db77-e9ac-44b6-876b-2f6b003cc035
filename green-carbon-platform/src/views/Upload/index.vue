<template>
  <div class="upload-page">
    <a-card title="文件上传管理中心 (核心功能)" class="page-card">
      <p>支持多格式文件上传，自动元数据提取和智能分类</p>
      
      <a-row :gutter="[16, 16]">
        <a-col :span="12">
          <a-card size="small" title="文件上传区域" class="upload-card">
            <a-upload-dragger
              v-model:file-list="fileList"
              :multiple="true"
              :accept="uploadConfig.accept"
              :before-upload="beforeUpload"
              @change="handleUploadChange"
              class="upload-dragger"
            >
              <p class="ant-upload-drag-icon">
                <a-icon type="inbox" />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">
                支持多种格式：PDF、Word、Excel、PPT等
              </p>
            </a-upload-dragger>
            
            <div class="upload-stats" style="margin-top: 16px;">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-statistic title="今日上传" :value="uploadStats.today" />
                </a-col>
                <a-col :span="12">
                  <a-statistic title="总文件数" :value="uploadStats.total" />
                </a-col>
              </a-row>
            </div>
          </a-card>
        </a-col>
        
        <a-col :span="12">
          <a-card size="small" title="文件类型分类" class="types-card">
            <a-tabs v-model:activeKey="activeFileType" size="small">
              <a-tab-pane v-for="(type, key) in fileTypes" :key="key" :tab="type.name">
                <div class="file-type-info">
                  <p>{{ type.description }}</p>
                  <div class="file-formats">
                    <a-tag v-for="format in type.accept.split(',')" :key="format" color="blue">
                      {{ format }}
                    </a-tag>
                  </div>
                  <div class="type-stats">
                    <a-statistic title="文件数量" :value="type.count || 0" />
                  </div>
                </div>
              </a-tab-pane>
            </a-tabs>
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col :span="24">
          <a-card size="small" title="上传文件管理" class="files-card">
            <a-table :columns="columns" :dataSource="uploadedFiles" :pagination="pagination">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'type'">
                  <a-tag :color="getFileTypeColor(record.type)">{{ record.type }}</a-tag>
                </template>
                <template v-if="column.key === 'status'">
                  <a-tag :color="record.status === '已处理' ? 'green' : 'orange'">
                    {{ record.status }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'actions'">
                  <a-space>
                    <a-button size="small" type="primary" @click="viewFile(record)">
                      查看
                    </a-button>
                    <a-button size="small" @click="downloadFile(record)">
                      下载
                    </a-button>
                    <a-button size="small" danger @click="deleteFile(record)">
                      删除
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'

// 当前文件类型
const activeFileType = ref('policy')

// 文件列表
const fileList = ref([])

// 上传配置
const uploadConfig = reactive({
  accept: '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar',
  maxSize: 100 * 1024 * 1024, // 100MB
  maxCount: 10
})

// 文件类型配置
const fileTypes = reactive({
  policy: {
    name: '政策文件',
    accept: '.pdf,.doc,.docx',
    description: '支持政策法规、规划方案类文档',
    count: 234
  },
  standard: {
    name: '标准规范',
    accept: '.pdf,.doc,.docx',
    description: '支持国家标准、行业标准、团体标准',
    count: 156
  },
  technology: {
    name: '技术文档',
    accept: '.pdf,.doc,.docx,.ppt,.pptx',
    description: '支持技术方案、研发报告、专利文档',
    count: 89
  },
  finance: {
    name: '金融产品',
    accept: '.pdf,.doc,.docx,.xls,.xlsx',
    description: '支持金融产品介绍、投资方案',
    count: 67
  },
  data: {
    name: '数据文件',
    accept: '.xls,.xlsx,.csv,.json',
    description: '支持统计数据、监测数据、报表',
    count: 123
  }
})

// 上传统计
const uploadStats = reactive({
  today: 45,
  total: 1234
})

// 表格列定义
const columns = [
  {
    title: '文件名称',
    dataIndex: 'name',
    key: 'name',
    width: '25%'
  },
  {
    title: '文件类型',
    dataIndex: 'type',
    key: 'type',
    width: '15%'
  },
  {
    title: '文件大小',
    dataIndex: 'size',
    key: 'size',
    width: '10%'
  },
  {
    title: '上传时间',
    dataIndex: 'uploadTime',
    key: 'uploadTime',
    width: '20%'
  },
  {
    title: '处理状态',
    dataIndex: 'status',
    key: 'status',
    width: '15%'
  },
  {
    title: '操作',
    key: 'actions',
    width: '15%'
  }
]

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 50,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 项，共 ${total} 项`
})

// 上传的文件列表
const uploadedFiles = ref([
  {
    key: '1',
    name: '碳达峰碳中和实施方案.pdf',
    type: '政策文件',
    size: '2.5MB',
    uploadTime: '2025-01-15 14:30',
    status: '已处理'
  },
  {
    key: '2',
    name: '绿色金融产品介绍.docx',
    type: '金融产品',
    size: '1.8MB',
    uploadTime: '2025-01-15 14:25',
    status: '处理中'
  },
  {
    key: '3',
    name: '环保技术标准.pdf',
    type: '标准规范',
    size: '3.2MB',
    uploadTime: '2025-01-15 14:20',
    status: '已处理'
  }
])

// 上传前检查
const beforeUpload = (file) => {
  const isValidSize = file.size <= uploadConfig.maxSize
  if (!isValidSize) {
    message.error('文件大小不能超过100MB!')
    return false
  }
  
  const isValidType = uploadConfig.accept.includes(file.name.substring(file.name.lastIndexOf('.')))
  if (!isValidType) {
    message.error('不支持的文件格式!')
    return false
  }
  
  return true
}

// 上传状态变化
const handleUploadChange = (info) => {
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 上传成功`)
    uploadStats.today++
    uploadStats.total++
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 上传失败`)
  }
}

// 获取文件类型颜色
const getFileTypeColor = (type) => {
  const colorMap = {
    '政策文件': 'blue',
    '标准规范': 'green',
    '技术文档': 'orange',
    '金融产品': 'purple',
    '数据文件': 'cyan'
  }
  return colorMap[type] || 'default'
}

// 查看文件
const viewFile = (record) => {
  message.info(`查看文件: ${record.name}`)
}

// 下载文件
const downloadFile = (record) => {
  message.info(`下载文件: ${record.name}`)
}

// 删除文件
const deleteFile = (record) => {
  message.info(`删除文件: ${record.name}`)
}
</script>

<style scoped>
.upload-page {
  padding: 24px;
}

.upload-card,
.types-card,
.files-card {
  height: auto;
}

.upload-dragger {
  padding: 40px 0;
}

.file-type-info {
  padding: 16px;
  min-height: 200px;
}

.file-formats {
  margin: 16px 0;
}

.type-stats {
  margin-top: 16px;
}

.upload-stats {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}
</style> 