<template>
  <div class="innovation-page">
    <a-card title="综合创新服务中心 (九大模块-9)" class="page-card">
      <p>7大创新服务功能的综合平台</p>
      
      <a-row :gutter="[16, 16]">
        <a-col :span="12" v-for="(service, index) in innovationServices" :key="index">
          <a-card size="small" :title="service.name" class="innovation-card">
            <div class="innovation-content">
              <p>{{ service.description }}</p>
              <div class="innovation-tags">
                <a-tag v-for="tag in service.tags" :key="tag" color="purple">
                  {{ tag }}
                </a-tag>
              </div>
              <div class="innovation-stats">
                <a-statistic title="服务项目" :value="service.projects" />
                <a-statistic title="成功率" :value="service.successRate" suffix="%" />
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 7大创新服务功能
const innovationServices = ref([
  {
    name: '碳资产开发',
    description: 'CCER项目申报辅导和碳资产管理',
    tags: ['CCER', '碳资产', '审定核查'],
    projects: 156,
    successRate: 85
  },
  {
    name: '国际智库合作',
    description: '低碳技术转移和国际合作对接',
    tags: ['国际合作', '技术转移', '智库'],
    projects: 89,
    successRate: 78
  },
  {
    name: '绿色诊断',
    description: '企业低碳发展综合评估诊断',
    tags: ['绿色诊断', '评估', '咨询'],
    projects: 234,
    successRate: 92
  },
  {
    name: '资源整合',
    description: '跨行业低碳资源整合对接',
    tags: ['资源整合', '跨行业', '对接'],
    projects: 123,
    successRate: 87
  },
  {
    name: '产融对接',
    description: '绿色项目投融资服务对接',
    tags: ['产融对接', '投融资', '绿色项目'],
    projects: 167,
    successRate: 76
  },
  {
    name: '双碳服务',
    description: '碳达峰碳中和路径规划服务',
    tags: ['双碳', '路径规划', '碳中和'],
    projects: 98,
    successRate: 83
  },
  {
    name: '资产管理',
    description: '碳资产管理与交易服务',
    tags: ['资产管理', '碳交易', '管理'],
    projects: 145,
    successRate: 89
  }
])
</script>

<style scoped>
.innovation-page {
  padding: 24px;
}

.innovation-card {
  height: 250px;
}

.innovation-content {
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.innovation-tags {
  margin: 8px 0;
}

.innovation-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}
</style> 