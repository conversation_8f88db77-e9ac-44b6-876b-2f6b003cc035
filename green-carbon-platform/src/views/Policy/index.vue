<template>
  <div class="policy-platform">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <span class="title-icon">📜</span>
            政策法规服务平台
          </h1>
          <p class="page-subtitle">整合ZA-ZP分类体系 + 法律层级 + 标准库的综合服务</p>
        </div>
        <div class="header-stats">
          <a-statistic title="政策文件" :value="2345" suffix="份" />
          <a-statistic title="法律法规" :value="567" suffix="部" />
          <a-statistic title="标准规范" :value="1234" suffix="项" />
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <a-card class="search-card">
        <div class="search-container">
          <div class="main-search">
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="搜索政策法规、标准规范..."
              size="large"
              @search="handleSearch"
              enterButton
              class="search-input"
            />
          </div>
          
          <div class="filter-tabs">
            <a-tabs v-model:activeKey="activeFilter" @change="handleFilterChange">
              <a-tab-pane key="all" tab="全部">
                <div class="filter-options">
                  <a-space wrap>
                    <a-select v-model:value="selectedLevel" placeholder="政策级别" style="width: 120px">
                      <a-select-option value="">全部级别</a-select-option>
                      <a-select-option value="national">国家级</a-select-option>
                      <a-select-option value="ministry">部委级</a-select-option>
                      <a-select-option value="local">地方级</a-select-option>
                    </a-select>
                    
                    <a-range-picker v-model:value="dateRange" />
                    
                    <a-button type="primary" @click="applyFilters">
                      应用筛选 <a-icon type="filter" />
                    </a-button>
                    <a-button @click="resetFilters">
                      重置 <a-icon type="reload" />
                    </a-button>
                  </a-space>
                </div>
              </a-tab-pane>
              
              <a-tab-pane key="policy" tab="政策分类">
                <div class="category-filters">
                  <a-space wrap>
                    <a-tag
                      v-for="category in POLICY_CATEGORIES"
                      :key="category.code"
                      :color="selectedCategories.includes(category.code) ? 'blue' : 'default'"
                      @click="toggleCategory(category.code)"
                      class="category-tag"
                    >
                      {{ category.code }} - {{ category.name }}
                    </a-tag>
                  </a-space>
                </div>
              </a-tab-pane>
              
              <a-tab-pane key="legal" tab="法律层级">
                <div class="legal-filters">
                  <a-space wrap>
                    <a-tag
                      v-for="legal in LEGAL_HIERARCHY"
                      :key="legal.level"
                      :color="selectedLegalLevels.includes(legal.level) ? 'green' : 'default'"
                      @click="toggleLegalLevel(legal.level)"
                      class="legal-tag"
                    >
                      {{ legal.name }}
                    </a-tag>
                  </a-space>
                </div>
              </a-tab-pane>
              
              <a-tab-pane key="standard" tab="标准类型">
                <div class="standard-filters">
                  <a-space wrap>
                    <a-tag
                      v-for="standard in STANDARD_CATEGORIES"
                      :key="standard.type"
                      :color="selectedStandards.includes(standard.type) ? 'purple' : 'default'"
                      @click="toggleStandard(standard.type)"
                      class="standard-tag"
                    >
                      {{ standard.level }}
                    </a-tag>
                  </a-space>
                </div>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 主内容区域 -->
    <div class="content-section">
      <a-row :gutter="24">
        <!-- 左侧分类导航 -->
        <a-col :xs="24" :lg="6">
          <a-card title="分类导航" class="navigation-card">
            <a-collapse v-model:activeKey="activeNavigation" ghost>
              <!-- 政策分类 -->
              <a-collapse-panel key="policy" header="政策分类 (ZA-ZP体系)">
                <template #extra>
                  <a-badge :count="policyCount" />
                </template>
                <div class="nav-category">
                  <div
                    v-for="category in POLICY_CATEGORIES"
                    :key="category.code"
                    class="nav-item"
                    :class="{ active: selectedCategories.includes(category.code) }"
                    @click="selectNavCategory(category.code)"
                  >
                    <div class="nav-item-header">
                      <span class="nav-code">{{ category.code }}</span>
                      <span class="nav-name">{{ category.name }}</span>
                      <a-badge :count="category.subCategories.reduce((sum, sub) => sum + sub.count, 0)" />
                    </div>
                    <div class="nav-description">{{ category.description }}</div>
                    <div class="nav-examples">
                      <a-tag v-for="example in category.examples" :key="example" size="small">
                        {{ example }}
                      </a-tag>
                    </div>
                  </div>
                </div>
              </a-collapse-panel>

              <!-- 法律层级 -->
              <a-collapse-panel key="legal" header="法律法规层级">
                <template #extra>
                  <a-badge :count="legalCount" />
                </template>
                <div class="nav-category">
                  <div
                    v-for="legal in LEGAL_HIERARCHY"
                    :key="legal.level"
                    class="nav-item"
                    :class="{ active: selectedLegalLevels.includes(legal.level) }"
                    @click="selectNavLegal(legal.level)"
                  >
                    <div class="nav-item-header">
                      <span class="nav-name">{{ legal.name }}</span>
                      <a-badge :count="legal.count" />
                    </div>
                    <div class="nav-description">{{ legal.description }}</div>
                    <div class="nav-status">
                      <a-tag :color="legal.status === '现行有效' ? 'green' : 'orange'">
                        {{ legal.status }}
                      </a-tag>
                    </div>
                  </div>
                </div>
              </a-collapse-panel>

              <!-- 标准分类 -->
              <a-collapse-panel key="standard" header="标准规范体系">
                <template #extra>
                  <a-badge :count="standardCount" />
                </template>
                <div class="nav-category">
                  <div
                    v-for="standard in STANDARD_CATEGORIES"
                    :key="standard.type"
                    class="nav-item"
                    :class="{ active: selectedStandards.includes(standard.type) }"
                    @click="selectNavStandard(standard.type)"
                  >
                    <div class="nav-item-header">
                      <span class="nav-name">{{ standard.level }}</span>
                      <a-badge :count="standard.count" />
                    </div>
                    <div class="nav-description">{{ standard.domain }}</div>
                    <div class="nav-examples">
                      <a-tag size="small" color="blue">{{ standard.example }}</a-tag>
                    </div>
                  </div>
                </div>
              </a-collapse-panel>
            </a-collapse>
          </a-card>
        </a-col>

        <!-- 中间内容列表 -->
        <a-col :xs="24" :lg="12">
          <a-card title="搜索结果" class="results-card">
            <template #extra>
              <a-space>
                <span>共 {{ totalResults }} 条结果</span>
                <a-select v-model:value="sortBy" style="width: 120px">
                  <a-select-option value="time">按时间</a-select-option>
                  <a-select-option value="relevance">按相关性</a-select-option>
                  <a-select-option value="title">按标题</a-select-option>
                </a-select>
              </a-space>
            </template>
            
            <a-list
              :data-source="searchResults"
              :pagination="paginationConfig"
              class="results-list"
            >
              <template #renderItem="{ item }">
                <a-list-item class="result-item">
                  <div class="result-content">
                    <div class="result-header">
                      <h4 class="result-title">{{ item.title }}</h4>
                      <div class="result-tags">
                        <a-tag :color="getTypeColor(item.type)">{{ item.type }}</a-tag>
                        <a-tag v-if="item.category">{{ item.category }}</a-tag>
                        <a-tag v-if="item.level" color="green">{{ item.level }}</a-tag>
                      </div>
                    </div>
                    
                    <div class="result-summary">{{ item.summary }}</div>
                    
                    <div class="result-meta">
                      <a-space>
                        <span><a-icon type="calendar" /> {{ item.publishDate }}</span>
                        <span><a-icon type="team" /> {{ item.publisher }}</span>
                        <span><a-icon type="eye" /> {{ item.views }}</span>
                        <span><a-icon type="download" /> {{ item.downloads }}</span>
                      </a-space>
                    </div>
                    
                    <div class="result-keywords">
                      <a-tag v-for="keyword in item.keywords" :key="keyword" size="small">
                        {{ keyword }}
                      </a-tag>
                    </div>
                    
                    <div class="result-actions">
                      <a-space>
                        <a-button type="primary" size="small" @click="viewDetail(item)">
                          <a-icon type="eye" /> 查看详情
                        </a-button>
                        <a-button size="small" @click="downloadFile(item)">
                          <a-icon type="download" /> 下载文件
                        </a-button>
                        <a-button size="small" @click="analyzeWithAI(item)">
                          <a-icon type="robot" /> AI解读
                        </a-button>
                        <a-button size="small" @click="shareItem(item)">
                          <a-icon type="share-alt" /> 分享
                        </a-button>
                      </a-space>
                    </div>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>

        <!-- 右侧快速操作和统计 -->
        <a-col :xs="24" :lg="6">
          <a-space direction="vertical" style="width: 100%">
            <!-- 业务流程 -->
            <a-card title="业务流程" class="workflow-card">
              <a-steps direction="vertical" :current="currentStep" size="small">
                <a-step title="政策解读" description="智能解析政策内容" />
                <a-step title="企业行动指南" description="生成具体执行方案" />
                <a-step title="服务对接" description="匹配相关服务机构" />
              </a-steps>
              
              <div class="workflow-actions">
                <a-button type="primary" block @click="startWorkflow">
                  <a-icon type="play-circle" /> 开始业务流程
                </a-button>
              </div>
            </a-card>

            <!-- 快速统计 -->
            <a-card title="统计信息" class="stats-card">
              <div class="stat-grid">
                <div class="stat-item">
                  <div class="stat-icon policy">📋</div>
                  <div class="stat-content">
                    <div class="stat-number">{{ policyStats.total }}</div>
                    <div class="stat-label">政策文件</div>
                  </div>
                </div>
                
                <div class="stat-item">
                  <div class="stat-icon legal">⚖️</div>
                  <div class="stat-content">
                    <div class="stat-number">{{ legalStats.total }}</div>
                    <div class="stat-label">法律法规</div>
                  </div>
                </div>
                
                <div class="stat-item">
                  <div class="stat-icon standard">📐</div>
                  <div class="stat-content">
                    <div class="stat-number">{{ standardStats.total }}</div>
                    <div class="stat-label">标准规范</div>
                  </div>
                </div>
                
                <div class="stat-item">
                  <div class="stat-icon update">🔄</div>
                  <div class="stat-content">
                    <div class="stat-number">{{ recentUpdates }}</div>
                    <div class="stat-label">今日更新</div>
                  </div>
                </div>
              </div>
            </a-card>

            <!-- 热门政策 -->
            <a-card title="热门政策" class="hot-policies-card">
              <a-list
                :data-source="hotPolicies"
                size="small"
                class="hot-list"
              >
                <template #renderItem="{ item, index }">
                  <a-list-item class="hot-item">
                    <div class="hot-rank">{{ index + 1 }}</div>
                    <div class="hot-content">
                      <div class="hot-title">{{ item.title }}</div>
                      <div class="hot-meta">
                        <a-tag size="small" :color="getTypeColor(item.type)">{{ item.type }}</a-tag>
                        <span class="hot-views">{{ item.views }} 浏览</span>
                      </div>
                    </div>
                  </a-list-item>
                </template>
              </a-list>
            </a-card>
          </a-space>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
// import { POLICY_CATEGORIES, LEGAL_HIERARCHY, STANDARD_CATEGORIES } from '@/constants'

// 临时数据，避免导入错误
const POLICY_CATEGORIES = [
  { code: 'ZA', name: '综合政务', subCategories: [] },
  { code: 'ZB', name: '经济管理', subCategories: [] }
]

const LEGAL_HIERARCHY = [
  { level: 'national_law', name: '国家法律', count: 45 },
  { level: 'regulation', name: '行政法规', count: 123 }
]

const STANDARD_CATEGORIES = [
  { type: 'national_standard', level: '国家标准(GB)', count: 456 },
  { type: 'industry_standard', level: '行业标准(HY/T)', count: 234 }
]

// 搜索和筛选状态
const searchKeyword = ref('')
const activeFilter = ref('all')
const selectedLevel = ref('')
const dateRange = ref([])
const selectedCategories = ref([])
const selectedLegalLevels = ref([])
const selectedStandards = ref([])
const sortBy = ref('time')

// 导航状态
const activeNavigation = ref(['policy', 'legal', 'standard'])

// 业务流程状态
const currentStep = ref(0)

// 搜索结果
const searchResults = ref([
  {
    id: 1,
    title: '关于完整准确全面贯彻新发展理念做好碳达峰碳中和工作的意见',
    type: '综合政务',
    category: 'ZA',
    level: '国家级',
    summary: '为完整准确全面贯彻新发展理念，做好碳达峰、碳中和工作，现提出如下意见...',
    publishDate: '2021-10-24',
    publisher: '中共中央、国务院',
    views: 15234,
    downloads: 5678,
    keywords: ['碳达峰', '碳中和', '新发展理念', '绿色发展']
  },
  {
    id: 2,
    title: '绿色金融发展指导意见实施细则',
    type: '经济管理',
    category: 'ZB',
    level: '部委级',
    summary: '为推动绿色金融规范发展，明确各类绿色金融产品标准...',
    publishDate: '2023-08-15',
    publisher: '中国人民银行',
    views: 8956,
    downloads: 3421,
    keywords: ['绿色金融', '实施细则', '金融产品', '标准规范']
  },
  {
    id: 3,
    title: 'GB/T 32150-2015 工业企业温室气体排放核算和报告通则',
    type: '标准规范',
    category: 'GB',
    level: '国家标准',
    summary: '本标准规定了工业企业温室气体排放核算和报告的基本原则、核算方法...',
    publishDate: '2015-12-10',
    publisher: '国家质量监督检验检疫总局',
    views: 12345,
    downloads: 4567,
    keywords: ['温室气体', '排放核算', '工业企业', '报告通则']
  }
])

// 热门政策
const hotPolicies = ref([
  { title: '关于促进绿色消费的实施方案', type: '综合政务', views: 8234 },
  { title: '绿色低碳转型产业政策', type: '经济管理', views: 7156 },
  { title: '碳排放权交易管理办法', type: '法律法规', views: 6789 },
  { title: '绿色建筑评价标准', type: '标准规范', views: 5432 },
  { title: '新能源汽车推广应用财政支持政策', type: '财政政策', views: 4123 }
])

// 统计数据
const policyStats = ref({ total: 2345 })
const legalStats = ref({ total: 567 })
const standardStats = ref({ total: 1234 })
const recentUpdates = ref(23)

// 计算属性
const totalResults = computed(() => searchResults.value.length)
const policyCount = computed(() => POLICY_CATEGORIES.reduce((sum, cat) => sum + cat.subCategories.reduce((subSum, sub) => subSum + sub.count, 0), 0))
const legalCount = computed(() => LEGAL_HIERARCHY.reduce((sum, legal) => sum + legal.count, 0))
const standardCount = computed(() => STANDARD_CATEGORIES.reduce((sum, std) => sum + std.count, 0))

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}

// 方法
const handleSearch = (value) => {
  console.log('搜索:', value)
}

const handleFilterChange = (key) => {
  console.log('切换筛选:', key)
}

const toggleCategory = (code) => {
  const index = selectedCategories.value.indexOf(code)
  if (index > -1) {
    selectedCategories.value.splice(index, 1)
  } else {
    selectedCategories.value.push(code)
  }
}

const toggleLegalLevel = (level) => {
  const index = selectedLegalLevels.value.indexOf(level)
  if (index > -1) {
    selectedLegalLevels.value.splice(index, 1)
  } else {
    selectedLegalLevels.value.push(level)
  }
}

const toggleStandard = (type) => {
  const index = selectedStandards.value.indexOf(type)
  if (index > -1) {
    selectedStandards.value.splice(index, 1)
  } else {
    selectedStandards.value.push(type)
  }
}

const selectNavCategory = (code) => {
  toggleCategory(code)
}

const selectNavLegal = (level) => {
  toggleLegalLevel(level)
}

const selectNavStandard = (type) => {
  toggleStandard(type)
}

const applyFilters = () => {
  console.log('应用筛选条件')
}

const resetFilters = () => {
  selectedLevel.value = ''
  dateRange.value = []
  selectedCategories.value = []
  selectedLegalLevels.value = []
  selectedStandards.value = []
}

const getTypeColor = (type) => {
  const colorMap = {
    '综合政务': 'blue',
    '经济管理': 'green',
    '法律法规': 'purple',
    '标准规范': 'orange',
    '财政政策': 'red'
  }
  return colorMap[type] || 'default'
}

const viewDetail = (item) => {
  console.log('查看详情:', item)
}

const downloadFile = (item) => {
  console.log('下载文件:', item)
}

const analyzeWithAI = (item) => {
  console.log('AI解读:', item)
}

const shareItem = (item) => {
  console.log('分享:', item)
}

const startWorkflow = () => {
  currentStep.value = 0
  console.log('开始业务流程')
}

onMounted(() => {
  console.log('政策法规服务平台初始化完成')
})
</script>

<style scoped>
.policy-platform {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部 */
.page-header {
  background: var(--gradient-primary);
  padding: 40px 24px;
  margin-bottom: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 2.5rem;
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  margin-bottom: 0;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.header-stats .ant-statistic {
  color: white;
}

.header-stats .ant-statistic-title {
  color: rgba(255, 255, 255, 0.8);
}

.header-stats .ant-statistic-content {
  color: white;
}

/* 搜索区域 */
.search-section {
  padding: 0 24px;
  margin-bottom: 24px;
}

.search-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.search-container {
  padding: 8px 0;
}

.main-search {
  margin-bottom: 24px;
}

.search-input {
  font-size: 16px;
}

.filter-tabs .ant-tabs-content-holder {
  padding-top: 16px;
}

.filter-options {
  min-height: 40px;
}

.category-filters,
.legal-filters,
.standard-filters {
  min-height: 60px;
}

.category-tag,
.legal-tag,
.standard-tag {
  cursor: pointer;
  margin: 4px;
  transition: all 0.3s ease;
}

.category-tag:hover,
.legal-tag:hover,
.standard-tag:hover {
  transform: scale(1.05);
}

/* 内容区域 */
.content-section {
  padding: 0 24px;
}

/* 左侧导航 */
.navigation-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
}

.nav-category {
  max-height: 600px;
  overflow-y: auto;
}

.nav-item {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.nav-item:hover {
  background: #f9f9f9;
  border-color: var(--primary-color);
}

.nav-item.active {
  background: #e6f7ff;
  border-color: #1890ff;
}

.nav-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.nav-code {
  font-weight: 600;
  color: var(--primary-color);
}

.nav-name {
  font-weight: 600;
  color: #262626;
  flex: 1;
  margin-left: 8px;
}

.nav-description {
  font-size: 0.85rem;
  color: #8c8c8c;
  line-height: 1.4;
  margin-bottom: 8px;
}

.nav-examples {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.nav-status {
  margin-top: 4px;
}

/* 搜索结果 */
.results-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
}

.results-list .ant-list-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 0;
}

.result-item {
  width: 100%;
}

.result-content {
  width: 100%;
}

.result-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.result-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #262626;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  margin-right: 16px;
}

.result-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.result-summary {
  color: #595959;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 12px;
}

.result-meta {
  margin-bottom: 12px;
}

.result-meta span {
  color: #8c8c8c;
  font-size: 0.85rem;
}

.result-keywords {
  margin-bottom: 16px;
}

.result-actions {
  text-align: left;
}

/* 右侧卡片 */
.workflow-card,
.stats-card,
.hot-policies-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
}

.workflow-actions {
  margin-top: 16px;
}

.stat-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 8px;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.stat-icon.policy { background: linear-gradient(135deg, #1890ff, #69c0ff); }
.stat-icon.legal { background: linear-gradient(135deg, #52c41a, #95de64); }
.stat-icon.standard { background: linear-gradient(135deg, #722ed1, #b37feb); }
.stat-icon.update { background: linear-gradient(135deg, #fa8c16, #ffc069); }

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.2rem;
  font-weight: 700;
  color: #262626;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 0.8rem;
  color: #8c8c8c;
}

/* 热门政策 */
.hot-list .ant-list-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.hot-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  width: 100%;
}

.hot-rank {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: var(--primary-color);
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.hot-content {
  flex: 1;
}

.hot-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #262626;
  line-height: 1.3;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.hot-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.hot-views {
  font-size: 0.75rem;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .header-stats {
    justify-content: center;
  }
  
  .search-section,
  .content-section {
    padding: 0 16px;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .stat-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .result-title {
    margin-right: 0;
  }
}

@media (max-width: 1200px) {
  .nav-category {
    max-height: 400px;
  }
}
</style> 