<template>
  <div class="policy-platform">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <span class="title-icon">📜</span>
            政策法规服务平台
          </h1>
          <p class="page-subtitle">整合ZA-ZP分类体系 + 法律层级 + 标准库的综合服务</p>
        </div>
        <div class="header-stats">
          <a-statistic title="政策文件" :value="2345" suffix="份" />
          <a-statistic title="法律法规" :value="567" suffix="部" />
          <a-statistic title="标准规范" :value="1234" suffix="项" />
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <a-card>
        <div class="search-container">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索政策法规、标准规范..."
            size="large"
            @search="handleSearch"
            enterButton
            style="max-width: 600px; margin: 0 auto; display: block;"
          />
        </div>
      </a-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="[24, 24]">
        <!-- 政策分类 -->
        <a-col :xs="24" :lg="8">
          <a-card title="政策分类" size="small">
            <div class="category-list">
              <div 
                v-for="category in policyCategories" 
                :key="category.code"
                class="category-item"
                @click="selectCategory(category)"
                :class="{ active: selectedCategory === category.code }"
              >
                <div class="category-header">
                  <span class="category-code">{{ category.code }}</span>
                  <span class="category-name">{{ category.name }}</span>
                </div>
                <div class="category-count">{{ category.count }} 项</div>
              </div>
            </div>
          </a-card>
        </a-col>
        
        <!-- 法律层级 -->
        <a-col :xs="24" :lg="8">
          <a-card title="法律层级" size="small">
            <div class="hierarchy-list">
              <div 
                v-for="level in legalHierarchy" 
                :key="level.code"
                class="hierarchy-item"
              >
                <div class="hierarchy-header">
                  <span class="hierarchy-name">{{ level.name }}</span>
                  <a-tag :color="level.color">{{ level.status }}</a-tag>
                </div>
                <div class="hierarchy-count">{{ level.count }} 部</div>
              </div>
            </div>
          </a-card>
        </a-col>
        
        <!-- 标准库 -->
        <a-col :xs="24" :lg="8">
          <a-card title="标准库" size="small">
            <div class="standard-list">
              <div 
                v-for="standard in standardCategories" 
                :key="standard.type"
                class="standard-item"
              >
                <div class="standard-header">
                  <span class="standard-level">{{ standard.level }}</span>
                </div>
                <div class="standard-count">{{ standard.count }} 项</div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 搜索结果 -->
      <a-row style="margin-top: 24px;">
        <a-col :span="24">
          <a-card title="搜索结果" size="small">
            <a-list :data-source="searchResults" item-layout="vertical">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta
                    :title="item.title"
                    :description="item.description"
                  />
                  <template #actions>
                    <a-button size="small" type="link">查看详情</a-button>
                    <a-button size="small" type="link">下载</a-button>
                  </template>
                  <template #extra>
                    <a-tag :color="item.color">{{ item.type }}</a-tag>
                  </template>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 搜索关键词
const searchKeyword = ref('')
const selectedCategory = ref('')

// 政策分类数据
const policyCategories = ref([
  { code: 'ZA', name: '综合政务', count: 156 },
  { code: 'ZB', name: '经济管理', count: 234 },
  { code: 'ZC', name: '社会管理', count: 189 },
  { code: 'ZD', name: '环境保护', count: 298 }
])

// 法律层级数据
const legalHierarchy = ref([
  { code: 'constitution', name: '宪法及基本法律', count: 12, status: '现行有效', color: 'red' },
  { code: 'national_law', name: '国家法律', count: 45, status: '现行有效', color: 'blue' },
  { code: 'regulation', name: '行政法规', count: 123, status: '现行有效', color: 'green' },
  { code: 'local_law', name: '地方性法规', count: 267, status: '现行有效', color: 'orange' }
])

// 标准库数据
const standardCategories = ref([
  { type: 'national_standard', level: '国家标准(GB)', count: 456 },
  { type: 'industry_standard', level: '行业标准(HY/T)', count: 234 },
  { type: 'local_standard', level: '地方标准(DB)', count: 189 },
  { type: 'enterprise_standard', level: '企业标准(Q)', count: 123 }
])

// 搜索结果数据
const searchResults = ref([
  {
    id: 1,
    title: '关于完整准确全面贯彻新发展理念做好碳达峰碳中和工作的意见',
    description: '中共中央、国务院印发的关于碳达峰碳中和工作的纲领性文件',
    type: '政策文件',
    color: 'blue'
  },
  {
    id: 2,
    title: 'GB/T 32150-2015 工业企业温室气体排放核算和报告通则',
    description: '规定了工业企业温室气体排放核算和报告的原则、方法和要求',
    type: '国家标准',
    color: 'green'
  },
  {
    id: 3,
    title: '中华人民共和国环境保护法',
    description: '为保护和改善环境，防治污染和其他公害，保障公众健康',
    type: '国家法律',
    color: 'red'
  }
])

// 方法
const handleSearch = (value) => {
  console.log('搜索:', value)
  // 这里可以添加搜索逻辑
}

const selectCategory = (category) => {
  selectedCategory.value = category.code
  console.log('选择分类:', category)
  // 这里可以添加分类筛选逻辑
}
</script>

<style scoped>
.policy-platform {
  width: 100%;
  min-height: calc(100vh - 64px);
  background: #f8f9fa;
}

.page-header {
  background: linear-gradient(135deg, #2c5f2d 0%, #689a3d 50%, #97bc62 100%);
  padding: 32px 24px;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.page-title {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 2.5rem;
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  margin-bottom: 0;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.header-stats .ant-statistic {
  color: white;
}

.header-stats .ant-statistic-title {
  color: rgba(255, 255, 255, 0.8);
}

.header-stats .ant-statistic-content {
  color: white;
}

.search-section {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.main-content {
  padding: 0 24px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.category-item,
.hierarchy-item,
.standard-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-item:hover,
.hierarchy-item:hover,
.standard-item:hover {
  border-color: #2c5f2d;
  background: rgba(44, 95, 45, 0.05);
}

.category-item.active {
  border-color: #2c5f2d;
  background: rgba(44, 95, 45, 0.1);
}

.category-header,
.hierarchy-header,
.standard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.category-code {
  font-weight: bold;
  color: #2c5f2d;
}

.category-name,
.hierarchy-name,
.standard-level {
  font-weight: 500;
}

.category-count,
.hierarchy-count,
.standard-count {
  font-size: 12px;
  color: #666;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .header-stats {
    justify-content: center;
  }
  
  .search-section,
  .main-content {
    padding: 16px;
  }
}
</style>
