<template>
  <div class="home-container">
    <div class="home-content">
      <!-- 欢迎横幅 -->
      <div class="welcome-banner">
        <h1 class="banner-title">
          <span class="title-icon">🌱</span>
          国家绿色低碳全产业链服务平台
        </h1>
        <p class="banner-subtitle">
          整合政策法规、绿色金融、技术创新等九大模块，构建全方位绿色低碳服务生态
        </p>
        <div class="banner-stats">
          <div class="stat-item">
            <div class="stat-number">2024</div>
            <div class="stat-label">服务年度</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">9</div>
            <div class="stat-label">核心模块</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">11</div>
            <div class="stat-label">金融工具</div>
          </div>
        </div>
      </div>

      <!-- 核心指标卡片 -->
      <a-row :gutter="[16, 16]" class="metrics-row">
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6" :xxl="6" v-for="metric in coreMetrics" :key="metric.key">
          <div class="metric-card">
            <div class="metric-icon" :style="{ background: metric.gradient }">
              {{ metric.icon }}
            </div>
            <div class="metric-content">
              <div class="metric-value">
                {{ metric.value }}
                <span class="trend-indicator">{{ metric.trend }}</span>
              </div>
              <div class="metric-label">{{ metric.label }}</div>
            </div>
          </div>
        </a-col>
      </a-row>

      <!-- 九大核心模块 -->
      <div class="modules-section">
        <div class="section-title">
          <span class="title-icon">🎯</span>
          九大核心模块
        </div>
        
        <a-row :gutter="[16, 16]" class="modules-row">
          <a-col 
            :xs="24" :sm="12" :md="8" :lg="6" :xl="6" :xxl="4"
            v-for="module in platformModules" 
            :key="module.key"
          >
            <div class="module-card" @click="navigateToModule(module)">
              <div class="module-header">
                <div class="module-icon" :style="{ background: module.gradient }">
                  {{ module.icon }}
                </div>
                <div class="module-title">{{ module.title }}</div>
              </div>
              
              <div class="module-description">{{ module.description }}</div>
              
              <div class="module-stats">
                <div class="module-stat">
                  <div class="stat-value">{{ module.resourceCount }}</div>
                  <div class="stat-name">资源数量</div>
                </div>
                <div class="module-stat">
                  <div class="stat-value">{{ module.dailyVisits }}</div>
                  <div class="stat-name">日访问</div>
                </div>
                <div class="module-stat">
                  <div class="stat-value">{{ module.successRate }}%</div>
                  <div class="stat-name">成功率</div>
                </div>
              </div>
              
              <div class="module-action">
                <button class="action-btn" @click.stop="enterModule(module)">
                  进入模块
                </button>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 数据可视化和侧边栏 -->
      <a-row :gutter="[16, 16]" class="dashboard-row">
        <!-- 数据可视化区域 -->
        <a-col :xs="24" :lg="18" :xl="18" :xxl="20">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :lg="12">
              <div class="chart-card">
                <div class="chart-title">
                  <span>📊</span>
                  政策分类分布
                </div>
                <div class="chart-placeholder">
                  ZA-ZP政策分类分布统计图表
                </div>
              </div>
            </a-col>
            <a-col :xs="24" :lg="12">
              <div class="chart-card">
                <div class="chart-title">
                  <span>💰</span>
                  绿色金融工具分布
                </div>
                <div class="chart-placeholder">
                  11类绿色金融工具使用情况图表
                </div>
              </div>
            </a-col>
          </a-row>
        </a-col>
        
        <!-- 侧边栏区域 -->
        <a-col :xs="24" :lg="6" :xl="6" :xxl="4">
          <!-- 最新动态 -->
          <div class="sidebar-section">
            <div class="sidebar-card">
              <div class="sidebar-title">
                <span>📢</span>
                最新动态
              </div>
              <div class="news-item" v-for="news in latestNews" :key="news.id">
                <div class="news-tag" :class="news.type">{{ news.typeLabel }}</div>
                <div class="news-content">
                  <div class="news-title">{{ news.title }}</div>
                  <div class="news-time">{{ news.time }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 统计信息 -->
          <div class="sidebar-section">
            <div class="sidebar-card">
              <div class="sidebar-title">
                <span>📈</span>
                统计信息
              </div>
              <div class="stats-grid">
                <div class="stat-box" v-for="stat in quickStats" :key="stat.key">
                  <div class="stat-box-value">{{ stat.value }}</div>
                  <div class="stat-box-label">{{ stat.label }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 快捷工具 -->
          <div class="sidebar-section">
            <div class="sidebar-card">
              <div class="sidebar-title">
                <span>🛠️</span>
                快捷工具
              </div>
              <div class="tools-grid">
                <div 
                  v-for="tool in quickTools" 
                  :key="tool.key"
                  class="tool-item"
                  @click="useTool(tool)"
                >
                  <div class="tool-icon">{{ tool.icon }}</div>
                  <div class="tool-name">{{ tool.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 核心指标数据
const coreMetrics = ref([
  {
    key: 'policies',
    icon: '📋',
    value: '1234',
    label: '政策文件',
    trend: '↗15% 本月',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    key: 'finance',
    icon: '💰',
    value: '567',
    label: '金融产品',
    trend: '↗8% 本月',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
  },
  {
    key: 'tech',
    icon: '🚀',
    value: '890',
    label: '技术项目',
    trend: '↗23% 本月',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
  },
  {
    key: 'efficiency',
    icon: '⚡',
    value: '2.3',
    label: '平均处理时间(天)',
    trend: '↘12% 本月',
    gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
  }
])

// 九大平台模块
const platformModules = ref([
  {
    key: 'policy',
    title: '政策法规服务',
    description: 'ZA-ZP分类体系，涵盖国家、行业、地方三级政策',
    icon: '📖',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    resourceCount: 1234,
    dailyVisits: 856,
    successRate: 95,
    route: '/policy'
  },
  {
    key: 'finance',
    title: '绿色金融中心',
    description: '11类金融工具，服务绿色项目投融资需求',
    icon: '💰',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    resourceCount: 567,
    dailyVisits: 642,
    successRate: 88,
    route: '/finance'
  },
  {
    key: 'tech',
    title: '技术创新平台',
    description: '7大技术领域，推动绿色技术研发与应用',
    icon: '🔬',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    resourceCount: 890,
    dailyVisits: 723,
    successRate: 92,
    route: '/technology'
  },
  {
    key: 'supply',
    title: '供应链服务',
    description: '10大服务模块，构建绿色供应链生态',
    icon: '🔗',
    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    resourceCount: 456,
    dailyVisits: 534,
    successRate: 90,
    route: '/supply-chain'
  },
  {
    key: 'data',
    title: '数据分析决策',
    description: 'GB/T标准元数据管理，支撑科学决策',
    icon: '📊',
    gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    resourceCount: 789,
    dailyVisits: 445,
    successRate: 87,
    route: '/data-analysis'
  },
  {
    key: 'innovation',
    title: '创新孵化服务',
    description: '支持绿色技术创新企业孵化发展',
    icon: '🌱',
    gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    resourceCount: 234,
    dailyVisits: 367,
    successRate: 93,
    route: '/innovation'
  },
  {
    key: 'benchmark',
    title: '基准测试平台',
    description: '行业对标分析，助力企业提升竞争力',
    icon: '📈',
    gradient: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)',
    resourceCount: 345,
    dailyVisits: 298,
    successRate: 91,
    route: '/benchmark'
  },
  {
    key: 'ai',
    title: 'AI智能分析',
    description: '人工智能赋能绿色低碳智能决策',
    icon: '🤖',
    gradient: 'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)',
    resourceCount: 678,
    dailyVisits: 512,
    successRate: 89,
    route: '/ai-analysis'
  },
  {
    key: 'resources',
    title: '资源管理中心',
    description: '统一资源管理，提供一站式服务入口',
    icon: '📚',
    gradient: 'linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%)',
    resourceCount: 567,
    dailyVisits: 423,
    successRate: 94,
    route: '/resources'
  }
])

// 最新动态
const latestNews = ref([
  {
    id: 1,
    type: 'policy',
    typeLabel: '政策',
    title: '国家发改委发布最新碳达峰行动方案',
    time: '2024-03-15',
    summary: '进一步明确了2030年前碳达峰的具体路径和措施'
  },
  {
    id: 2,
    type: 'tech',
    typeLabel: '技术',
    title: '新一代储能技术获得重大突破',
    time: '2024-03-14',
    summary: '能量密度提升30%，成本降低20%'
  },
  {
    id: 3,
    type: 'finance',
    typeLabel: '金融',
    title: '绿色债券发行规模创历史新高',
    time: '2024-03-13',
    summary: '今年一季度发行总额超过1500亿元'
  }
])

// 快速统计
const quickStats = ref([
  { key: 'users', value: '12.5K', label: '注册用户' },
  { key: 'projects', value: '3.2K', label: '在建项目' },
  { key: 'investment', value: '856亿', label: '投资总额' },
  { key: 'reduction', value: '2.1万吨', label: '减排量' }
])

// 快捷工具
const quickTools = ref([
  { key: 'upload', icon: '📤', name: '文档上传' },
  { key: 'ai', icon: '🤖', name: 'AI分析' },
  { key: 'search', icon: '🔍', name: '政策查询' },
  { key: 'export', icon: '📊', name: '数据导出' },
  { key: 'report', icon: '📋', name: '报告生成' },
  { key: 'help', icon: '💬', name: '在线咨询' }
])

// 方法
const navigateToModule = (module: any) => {
  router.push(module.route)
}

const enterModule = (module: any) => {
  router.push(module.route)
}

const useTool = (tool: any) => {
  console.log('使用工具:', tool.name)
  // 这里可以添加具体的工具使用逻辑
}
</script>

<style scoped>
.home-container {
  min-height: 100%;
  background: linear-gradient(135deg, #f8faf8 0%, #f0f4f0 100%);
  padding: 0;
  margin: 0;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.home-content {
  padding: 16px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  margin: 0;
}

/* Banner样式 */
.welcome-banner {
  background: linear-gradient(135deg, #2c5f2d 0%, #689a3d 50%, #97bc62 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 16px;
  color: white;
  text-align: center;
  box-shadow: 0 8px 24px rgba(44, 95, 45, 0.15);
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.welcome-banner::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
}

.banner-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.banner-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 20px;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.banner-stats {
  display: flex;
  justify-content: center;
  gap: 24px;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 80px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  display: block;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
  margin-top: 4px;
}

/* 核心指标卡片 */
.metrics-row {
  margin-bottom: 16px;
}

.metric-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafa 100%);
  border-radius: 16px;
  padding: 20px;
  height: 100px;
  display: flex;
  align-items: center;
  border: 1px solid rgba(44, 95, 45, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(44, 95, 45, 0.12);
  border-color: rgba(44, 95, 45, 0.2);
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  color: white;
  margin-right: 16px;
  flex-shrink: 0;
}

.metric-content {
  flex: 1;
  min-width: 0;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.trend-indicator {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

/* 九大核心模块 */
.modules-section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c5f2d;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.module-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafa 100%);
  border-radius: 16px;
  padding: 20px;
  height: 160px;
  border: 1px solid rgba(44, 95, 45, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
}

.module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(44, 95, 45, 0.15);
  border-color: rgba(44, 95, 45, 0.2);
}

.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2c5f2d 0%, #97bc62 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.module-card:hover::before {
  transform: scaleX(1);
}

.module-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.module-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  margin-right: 12px;
  flex-shrink: 0;
}

.module-title {
  font-size: 15px;
  font-weight: 600;
  color: #1a1a1a;
}

.module-description {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.module-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.module-stat {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #2c5f2d;
  display: block;
}

.stat-name {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
}

.module-action {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  background: linear-gradient(135deg, #2c5f2d 0%, #97bc62 100%);
  border: none;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(44, 95, 45, 0.3);
}

/* 数据可视化区域 */
.dashboard-row {
  margin-bottom: 16px;
}

.chart-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafa 100%);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(44, 95, 45, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
  height: 280px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c5f2d;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-placeholder {
  height: 200px;
  background: linear-gradient(135deg, #f0f4f0 0%, #e8f0e8 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 14px;
  border: 2px dashed #d0d7d0;
}

/* 侧边栏区域 */
.sidebar-section {
  margin-bottom: 16px;
}

.sidebar-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafa 100%);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(44, 95, 45, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
  margin-bottom: 16px;
}

.sidebar-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c5f2d;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 最新动态 */
.news-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.news-item:last-child {
  border-bottom: none;
}

.news-tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  flex-shrink: 0;
}

.news-tag.policy { background: #e6f7ff; color: #1890ff; }
.news-tag.tech { background: #f6ffed; color: #52c41a; }
.news-tag.finance { background: #fff2e8; color: #fa8c16; }

.news-content {
  flex: 1;
  min-width: 0;
}

.news-title {
  font-size: 12px;
  color: #1a1a1a;
  margin-bottom: 4px;
  line-height: 1.4;
  font-weight: 500;
}

.news-time {
  font-size: 10px;
  color: #999;
}

/* 统计信息网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.stat-box {
  text-align: center;
  padding: 12px 8px;
  background: linear-gradient(135deg, #f0f4f0 0%, #e8f0e8 100%);
  border-radius: 8px;
  border: 1px solid rgba(44, 95, 45, 0.1);
}

.stat-box-value {
  font-size: 18px;
  font-weight: 600;
  color: #2c5f2d;
  display: block;
}

.stat-box-label {
  font-size: 10px;
  color: #666;
  margin-top: 2px;
}

/* 快捷工具 */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.tool-item {
  text-align: center;
  padding: 12px 8px;
  background: linear-gradient(135deg, #f0f4f0 0%, #e8f0e8 100%);
  border-radius: 8px;
  border: 1px solid rgba(44, 95, 45, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-item:hover {
  background: linear-gradient(135deg, #2c5f2d 0%, #97bc62 100%);
  color: white;
  transform: translateY(-2px);
}

.tool-icon {
  font-size: 16px;
  margin-bottom: 4px;
}

.tool-name {
  font-size: 10px;
  font-weight: 500;
}

/* 动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(1deg); }
  50% { transform: translateY(-5px) rotate(-1deg); }
  75% { transform: translateY(-15px) rotate(0.5deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .modules-row .ant-col {
    width: 50% !important;
    flex: 0 0 50% !important;
    max-width: 50% !important;
  }
}

@media (max-width: 768px) {
  .home-content {
    padding: 12px;
  }
  
  .banner-title {
    font-size: 20px;
  }
  
  .banner-stats {
    gap: 12px;
  }
  
  .stat-item {
    padding: 8px 12px;
    min-width: 60px;
  }
  
  .metric-card {
    height: 80px;
    padding: 16px;
  }
  
  .module-card {
    height: 140px;
    padding: 16px;
  }
  
  .modules-row .ant-col {
    width: 100% !important;
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }
  
  .chart-card {
    height: 240px;
    padding: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .tools-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .banner-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .stat-item {
    margin: 0;
  }
  
  .metric-card {
    padding: 12px;
  }
  
  .module-card {
    padding: 12px;
    height: 120px;
  }
}

/* 修复Ant Design栅格系统 */
.ant-row {
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
}

.ant-col {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

/* 修复容器溢出 */
* {
  box-sizing: border-box;
  max-width: 100%;
  word-wrap: break-word;
}

.ant-row,
.ant-col,
.home-container,
.home-content {
  min-width: 0;
  overflow-wrap: break-word;
}
</style> 