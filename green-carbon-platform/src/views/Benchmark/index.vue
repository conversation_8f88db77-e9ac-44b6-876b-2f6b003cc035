<template>
  <div class="benchmark-page">
    <a-card title="标杆示范展示中心 (九大模块-8)" class="page-card">
      <p>5类标杆示范案例的展示和推广</p>
      
      <a-row :gutter="[16, 16]">
        <a-col :span="12" v-for="(benchmark, index) in benchmarkCases" :key="index">
          <a-card size="small" :title="benchmark.name" class="benchmark-card">
            <div class="benchmark-content">
              <p>{{ benchmark.description }}</p>
              <div class="benchmark-tags">
                <a-tag v-for="tag in benchmark.tags" :key="tag" color="orange">
                  {{ tag }}
                </a-tag>
              </div>
              <div class="benchmark-stats">
                <a-statistic title="案例数量" :value="benchmark.cases" />
                <a-statistic title="减排量" :value="benchmark.reduction" suffix="万吨CO2" />
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 5类标杆示范案例
const benchmarkCases = ref([
  {
    name: '绿色工厂示范',
    description: '工业企业绿色低碳转型典型案例',
    tags: ['绿色工厂', '清洁生产', '能效提升'],
    cases: 156,
    reduction: 1234.5
  },
  {
    name: '零碳园区示范',
    description: '产业园区碳中和实践案例',
    tags: ['零碳园区', '可再生能源', '循环经济'],
    cases: 89,
    reduction: 2345.8
  },
  {
    name: '绿色供应链示范',
    description: '供应链绿色化改造典型案例',
    tags: ['绿色供应链', '碳足迹', '循环利用'],
    cases: 234,
    reduction: 1567.2
  },
  {
    name: '绿色金融创新示范',
    description: '绿色金融产品和服务创新案例',
    tags: ['绿色金融', '碳金融', 'ESG投资'],
    cases: 123,
    reduction: 890.6
  },
  {
    name: '绿色技术应用示范',
    description: '前沿绿色技术应用推广案例',
    tags: ['绿色技术', 'CCUS', '储能技术'],
    cases: 78,
    reduction: 1789.3
  }
])
</script>

<style scoped>
.benchmark-page {
  padding: 24px;
}

.benchmark-card {
  height: 250px;
}

.benchmark-content {
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.benchmark-tags {
  margin: 8px 0;
}

.benchmark-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}
</style> 