<template>
  <div class="finance-platform">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <span class="title-icon">💰</span>
            绿色金融服务中心
          </h1>
          <p class="page-subtitle">基于GB/T 45490-2025标准的11类绿色金融工具完整服务体系</p>
        </div>
        <div class="header-stats">
          <a-statistic title="金融产品" :value="567" suffix="个" />
          <a-statistic title="服务机构" :value="234" suffix="家" />
          <a-statistic title="成功案例" :value="1890" suffix="项" />
        </div>
      </div>
    </div>

    <!-- 金融工具分类展示 -->
    <div class="finance-tools-section">
      <a-card class="tools-card">
        <template #title>
          <div class="card-title">
            <span class="card-icon">🏦</span>
            11类绿色金融工具
          </div>
        </template>
        
        <a-tabs v-model:activeKey="activeToolType" @change="handleToolChange" class="finance-tabs">
          <a-tab-pane
            v-for="(tool, index) in FINANCE_CATEGORIES"
            :key="tool.type"
            :tab="tool.name"
          >
            <div class="tool-content">
              <!-- 工具概览 -->
              <div class="tool-overview">
                <a-row :gutter="[24, 24]">
                  <a-col :xs="24" :md="16">
                    <div class="tool-info">
                      <div class="tool-header">
                        <div class="tool-icon-large" :style="{ backgroundColor: getToolColor(index) }">
                          <span :class="tool.icon"></span>
                        </div>
                        <div class="tool-details">
                          <h2 class="tool-title">{{ tool.name }}</h2>
                          <p class="tool-description">{{ getToolDescription(tool.type) }}</p>
                          <div class="tool-tags">
                            <a-tag color="blue">{{ tool.regulators[0] }}</a-tag>
                            <a-tag color="green">产品数量: {{ tool.count }}</a-tag>
                          </div>
                        </div>
                      </div>
                      
                      <!-- 服务对象 -->
                      <div class="service-targets">
                        <h4>服务对象</h4>
                        <div class="target-list">
                          <div
                            v-for="target in tool.targets"
                            :key="target"
                            class="target-item"
                          >
                            <a-icon type="user" />
                            {{ target }}
                          </div>
                        </div>
                      </div>
                      
                      <!-- 政策依据 -->
                      <div class="policy-basis">
                        <h4>政策依据</h4>
                        <div class="policy-list">
                          <a-tag
                            v-for="policy in tool.policies"
                            :key="policy"
                            color="purple"
                            class="policy-tag"
                          >
                            📄 {{ policy }}
                          </a-tag>
                        </div>
                      </div>
                    </div>
                  </a-col>
                  
                  <a-col :xs="24" :md="8">
                    <div class="tool-actions">
                      <a-card title="快速操作" size="small">
                        <a-space direction="vertical" style="width: 100%">
                          <a-button type="primary" block size="large" @click="applyForProduct(tool)">
                            <a-icon type="form" /> 产品申请
                          </a-button>
                          <a-button block @click="consultExpert(tool)">
                            <a-icon type="message" /> 专家咨询
                          </a-button>
                          <a-button block @click="downloadGuide(tool)">
                            <a-icon type="download" /> 申请指南
                          </a-button>
                          <a-button block @click="viewCases(tool)">
                            <a-icon type="eye" /> 成功案例
                          </a-button>
                        </a-space>
                      </a-card>
                      
                      <a-card title="监管机构" size="small" class="regulator-card">
                        <div class="regulator-list">
                          <div
                            v-for="regulator in tool.regulators"
                            :key="regulator"
                            class="regulator-item"
                          >
                            <div class="regulator-icon">🏛️</div>
                            <div class="regulator-name">{{ regulator }}</div>
                          </div>
                        </div>
                      </a-card>
                    </div>
                  </a-col>
                </a-row>
              </div>
              
              <!-- 金融产品列表 -->
              <div class="products-section">
                <div class="section-header">
                  <h3>{{ tool.name }}产品列表</h3>
                  <a-space>
                    <a-input-search
                      v-model:value="productSearchKeyword"
                      placeholder="搜索产品..."
                      style="width: 300px"
                      @search="searchProducts"
                    />
                    <a-select v-model:value="productFilter" style="width: 120px">
                      <a-select-option value="">全部产品</a-select-option>
                      <a-select-option value="active">在售</a-select-option>
                      <a-select-option value="hot">热门</a-select-option>
                    </a-select>
                  </a-space>
                </div>
                
                <a-row :gutter="[16, 16]">
                  <a-col
                    :xs="24" :sm="12" :lg="8"
                    v-for="product in getFilteredProducts(tool.type)"
                    :key="product.id"
                  >
                    <a-card hoverable class="product-card">
                      <template #cover>
                        <div class="product-cover" :style="{ background: getProductGradient(product.risk) }">
                          <div class="product-icon">💎</div>
                          <div class="product-risk">{{ product.risk }}风险</div>
                        </div>
                      </template>
                      
                      <a-card-meta :title="product.name" :description="product.description" />
                      
                      <div class="product-details">
                        <div class="product-meta">
                          <div class="meta-item">
                            <span class="meta-label">利率范围:</span>
                            <span class="meta-value">{{ product.interestRate }}</span>
                          </div>
                          <div class="meta-item">
                            <span class="meta-label">期限:</span>
                            <span class="meta-value">{{ product.term }}</span>
                          </div>
                          <div class="meta-item">
                            <span class="meta-label">额度:</span>
                            <span class="meta-value">{{ product.amount }}</span>
                          </div>
                        </div>
                        
                        <div class="product-features">
                          <a-tag
                            v-for="feature in product.features"
                            :key="feature"
                            size="small"
                            color="blue"
                          >
                            {{ feature }}
                          </a-tag>
                        </div>
                        
                        <div class="product-actions">
                          <a-button type="primary" size="small" @click="applyProduct(product)">
                            立即申请
                          </a-button>
                          <a-button size="small" @click="compareProduct(product)">
                            加入对比
                          </a-button>
                          <a-button size="small" @click="favoriteProduct(product)">
                            <a-icon type="heart" />
                          </a-button>
                        </div>
                      </div>
                    </a-card>
                  </a-col>
                </a-row>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>

    <!-- 智能匹配和服务 -->
    <div class="services-section">
      <a-row :gutter="[24, 24]">
        <!-- 智能产品匹配 -->
        <a-col :xs="24" :lg="12">
          <a-card title="智能产品匹配" class="matching-card">
            <template #extra>
              <a-icon type="robot" />
            </template>
            
            <div class="matching-form">
              <a-form layout="vertical">
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="企业类型">
                      <a-select v-model:value="matchingForm.companyType" placeholder="选择企业类型">
                        <a-select-option value="large">大型企业</a-select-option>
                        <a-select-option value="medium">中型企业</a-select-option>
                        <a-select-option value="small">小型企业</a-select-option>
                        <a-select-option value="startup">初创企业</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="项目类型">
                      <a-select v-model:value="matchingForm.projectType" placeholder="选择项目类型">
                        <a-select-option value="renewable">可再生能源</a-select-option>
                        <a-select-option value="energy_saving">节能减排</a-select-option>
                        <a-select-option value="clean_transport">清洁交通</a-select-option>
                        <a-select-option value="green_building">绿色建筑</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="资金需求">
                      <a-select v-model:value="matchingForm.fundingNeed" placeholder="选择资金规模">
                        <a-select-option value="small">100万以下</a-select-option>
                        <a-select-option value="medium">100万-1000万</a-select-option>
                        <a-select-option value="large">1000万-1亿</a-select-option>
                        <a-select-option value="huge">1亿以上</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="风险偏好">
                      <a-select v-model:value="matchingForm.riskPreference" placeholder="选择风险偏好">
                        <a-select-option value="low">低风险</a-select-option>
                        <a-select-option value="medium">中等风险</a-select-option>
                        <a-select-option value="high">高风险</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                
                <a-form-item>
                  <a-button type="primary" block size="large" @click="startMatching">
                    <a-icon type="search" /> 开始智能匹配
                  </a-button>
                </a-form-item>
              </a-form>
            </div>
            
            <!-- 匹配结果 -->
            <div v-if="matchingResults.length > 0" class="matching-results">
              <h4>推荐产品</h4>
              <div class="result-list">
                <div
                  v-for="result in matchingResults"
                  :key="result.id"
                  class="result-item"
                >
                  <div class="result-icon">{{ result.icon }}</div>
                  <div class="result-info">
                    <div class="result-title">{{ result.name }}</div>
                    <div class="result-match">匹配度: {{ result.matchScore }}%</div>
                  </div>
                  <div class="result-action">
                    <a-button size="small" type="primary">申请</a-button>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        
        <!-- ESG评价和环境效益 -->
        <a-col :xs="24" :lg="12">
          <a-card title="ESG评价与环境效益" class="esg-card">
            <template #extra>
              <a-icon type="bar-chart" />
            </template>
            
            <div class="esg-dashboard">
              <!-- ESG评分 -->
              <div class="esg-score">
                <div class="score-item">
                  <div class="score-circle" style="background: conic-gradient(#52c41a 0% 85%, #f0f0f0 85% 100%)">
                    <div class="score-value">85</div>
                  </div>
                  <div class="score-label">环境评分 (E)</div>
                </div>
                <div class="score-item">
                  <div class="score-circle" style="background: conic-gradient(#1890ff 0% 78%, #f0f0f0 78% 100%)">
                    <div class="score-value">78</div>
                  </div>
                  <div class="score-label">社会评分 (S)</div>
                </div>
                <div class="score-item">
                  <div class="score-circle" style="background: conic-gradient(#722ed1 0% 92%, #f0f0f0 92% 100%)">
                    <div class="score-value">92</div>
                  </div>
                  <div class="score-label">治理评分 (G)</div>
                </div>
              </div>
              
              <!-- 环境效益统计 -->
              <div class="environmental-benefits">
                <h4>累计环境效益</h4>
                <div class="benefit-grid">
                  <div class="benefit-item">
                    <div class="benefit-icon">🌱</div>
                    <div class="benefit-content">
                      <div class="benefit-value">12,456</div>
                      <div class="benefit-unit">吨CO₂减排</div>
                    </div>
                  </div>
                  <div class="benefit-item">
                    <div class="benefit-icon">💧</div>
                    <div class="benefit-content">
                      <div class="benefit-value">8,234</div>
                      <div class="benefit-unit">吨废水处理</div>
                    </div>
                  </div>
                  <div class="benefit-item">
                    <div class="benefit-icon">⚡</div>
                    <div class="benefit-content">
                      <div class="benefit-value">567</div>
                      <div class="benefit-unit">万度清洁能源</div>
                    </div>
                  </div>
                  <div class="benefit-item">
                    <div class="benefit-icon">🌳</div>
                    <div class="benefit-content">
                      <div class="benefit-value">3,456</div>
                      <div class="benefit-unit">棵等效植树</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <a-button type="primary" block @click="generateESGReport">
                <a-icon type="file-pdf" /> 生成ESG报告
              </a-button>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 金融机构和成功案例 -->
    <div class="institutions-section">
      <a-row :gutter="[24, 24]">
        <!-- 合作金融机构 -->
        <a-col :xs="24" :lg="14">
          <a-card title="合作金融机构" class="institutions-card">
            <div class="institutions-grid">
              <div
                v-for="institution in financialInstitutions"
                :key="institution.id"
                class="institution-item"
                @click="viewInstitution(institution)"
              >
                <div class="institution-logo">{{ institution.logo }}</div>
                <div class="institution-info">
                  <h4 class="institution-name">{{ institution.name }}</h4>
                  <p class="institution-type">{{ institution.type }}</p>
                  <div class="institution-stats">
                    <a-tag color="green">产品: {{ institution.productCount }}</a-tag>
                    <a-tag color="blue">成功案例: {{ institution.caseCount }}</a-tag>
                  </div>
                </div>
                <div class="institution-rating">
                  <a-rate :value="institution.rating" disabled allow-half />
                  <span class="rating-text">{{ institution.rating }}</span>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        
        <!-- 成功案例展示 -->
        <a-col :xs="24" :lg="10">
          <a-card title="成功案例" class="cases-card">
            <template #extra>
              <a-button type="link" @click="viewAllCases">查看全部</a-button>
            </template>
            
            <div class="cases-list">
              <div
                v-for="case_ in successCases"
                :key="case_.id"
                class="case-item"
                @click="viewCaseDetail(case_)"
              >
                <div class="case-image">
                  <div class="case-type-badge">{{ case_.type }}</div>
                </div>
                <div class="case-content">
                  <h4 class="case-title">{{ case_.title }}</h4>
                  <p class="case-summary">{{ case_.summary }}</p>
                  <div class="case-metrics">
                    <div class="metric">
                      <span class="metric-label">融资金额:</span>
                      <span class="metric-value">{{ case_.funding }}</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">减排量:</span>
                      <span class="metric-value">{{ case_.emission }}</span>
                    </div>
                  </div>
                  <div class="case-tags">
                    <a-tag v-for="tag in case_.tags" :key="tag" size="small">{{ tag }}</a-tag>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { FINANCE_CATEGORIES } from '@/constants/finance'

// 响应式数据
const activeToolType = ref('green_credit')
const productSearchKeyword = ref('')
const productFilter = ref('')

// 智能匹配表单
const matchingForm = ref({
  companyType: '',
  projectType: '',
  fundingNeed: '',
  riskPreference: ''
})

// 匹配结果
const matchingResults = ref([])

// 金融产品数据
const financialProducts = ref({
  green_credit: [
    {
      id: 1,
      name: '绿色项目贷款',
      description: '专门用于环保项目的优惠贷款产品',
      interestRate: '3.5%-5.2%',
      term: '1-10年',
      amount: '100万-5亿',
      risk: '低',
      features: ['利率优惠', '快速审批', '灵活还款']
    },
    {
      id: 2,
      name: '清洁能源贷款',
      description: '支持光伏、风电等清洁能源项目',
      interestRate: '3.8%-5.5%',
      term: '3-15年',
      amount: '500万-10亿',
      risk: '中',
      features: ['长期资金', '政策支持', '专业服务']
    }
  ],
  green_securities: [
    {
      id: 3,
      name: '绿色企业债',
      description: '支持绿色项目的企业债券发行',
      interestRate: '3.2%-4.8%',
      term: '3-7年',
      amount: '5亿-50亿',
      risk: '中',
      features: ['成本较低', '期限灵活', '政策支持']
    }
  ]
})

// 金融机构数据
const financialInstitutions = ref([
  {
    id: 1,
    name: '中国工商银行',
    type: '国有大型银行',
    logo: '🏦',
    productCount: 15,
    caseCount: 234,
    rating: 4.8
  },
  {
    id: 2,
    name: '兴业银行',
    type: '股份制商业银行',
    logo: '🏛️',
    productCount: 12,
    caseCount: 189,
    rating: 4.6
  },
  {
    id: 3,
    name: '浦发银行',
    type: '股份制商业银行',
    logo: '🏪',
    productCount: 10,
    caseCount: 156,
    rating: 4.5
  }
])

// 成功案例数据
const successCases = ref([
  {
    id: 1,
    title: '某光伏发电项目绿色贷款',
    summary: '通过绿色信贷支持，成功建设100MW光伏电站',
    type: '绿色信贷',
    funding: '8.5亿元',
    emission: '年减排12万吨CO₂',
    tags: ['光伏发电', '清洁能源', '减排']
  },
  {
    id: 2,
    title: '绿色建筑项目债券发行',
    summary: '发行绿色债券支持LEED认证建筑项目',
    type: '绿色债券',
    funding: '15亿元',
    emission: '年减排8万吨CO₂',
    tags: ['绿色建筑', 'LEED认证', '节能']
  }
])

// 计算属性和方法
const getToolColor = (index) => {
  const colors = ['#1890ff', '#52c41a', '#722ed1', '#fa8c16', '#13c2c2', '#eb2f96', '#f5222d', '#faad14', '#a0d911', '#1890ff', '#52c41a']
  return colors[index % colors.length]
}

const getToolDescription = (type) => {
  const descriptions = {
    green_credit: '为绿色项目提供优惠利率的信贷产品，支持企业绿色转型发展',
    green_securities: '通过发行绿色债券等证券产品，为大型绿色项目提供资金支持',
    green_insurance: '为环境风险和绿色项目提供保险保障服务',
    green_fund: '专门投资于绿色产业和项目的基金产品',
    green_trust: '以绿色项目为投资标的的信托产品',
    green_lease: '为绿色设备和项目提供融资租赁服务',
    green_bill: '基于绿色项目和贸易的票据融资产品',
    carbon_finance: '碳配额交易、CCER开发等碳金融服务',
    evaluation: '绿色项目评估认证和环境效益评价服务',
    benefit: '环境效益量化评估和监测服务',
    guarantee: '为绿色金融产品提供担保和风险分担机制'
  }
  return descriptions[type] || '绿色金融创新产品和服务'
}

const getFilteredProducts = (toolType) => {
  return financialProducts.value[toolType] || []
}

const getProductGradient = (risk) => {
  const gradients = {
    '低': 'linear-gradient(135deg, #52c41a, #95de64)',
    '中': 'linear-gradient(135deg, #1890ff, #69c0ff)',
    '高': 'linear-gradient(135deg, #fa8c16, #ffc069)'
  }
  return gradients[risk] || gradients['中']
}

// 事件处理方法
const handleToolChange = (key) => {
  console.log('切换金融工具:', key)
}

const applyForProduct = (tool) => {
  console.log('申请产品:', tool.name)
}

const consultExpert = (tool) => {
  console.log('专家咨询:', tool.name)
}

const downloadGuide = (tool) => {
  console.log('下载指南:', tool.name)
}

const viewCases = (tool) => {
  console.log('查看案例:', tool.name)
}

const searchProducts = (keyword) => {
  console.log('搜索产品:', keyword)
}

const applyProduct = (product) => {
  console.log('申请产品:', product.name)
}

const compareProduct = (product) => {
  console.log('对比产品:', product.name)
}

const favoriteProduct = (product) => {
  console.log('收藏产品:', product.name)
}

const startMatching = () => {
  // 模拟智能匹配
  matchingResults.value = [
    { id: 1, name: '绿色项目贷款', icon: '💰', matchScore: 95 },
    { id: 2, name: '清洁能源基金', icon: '⚡', matchScore: 87 },
    { id: 3, name: '绿色企业债', icon: '📊', matchScore: 82 }
  ]
}

const generateESGReport = () => {
  console.log('生成ESG报告')
}

const viewInstitution = (institution) => {
  console.log('查看机构:', institution.name)
}

const viewAllCases = () => {
  console.log('查看全部案例')
}

const viewCaseDetail = (case_) => {
  console.log('查看案例详情:', case_.title)
}

onMounted(() => {
  console.log('绿色金融服务中心初始化完成')
})
</script>

<style scoped>
.finance-platform {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部 */
.page-header {
  background: var(--gradient-primary);
  padding: 40px 24px;
  margin-bottom: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 2.5rem;
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  margin-bottom: 0;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.header-stats .ant-statistic {
  color: white;
}

.header-stats .ant-statistic-title {
  color: rgba(255, 255, 255, 0.8);
}

.header-stats .ant-statistic-content {
  color: white;
}

/* 金融工具区域 */
.finance-tools-section {
  padding: 0 24px;
  margin-bottom: 24px;
}

.tools-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.2rem;
  font-weight: 600;
}

.card-icon {
  font-size: 1.5rem;
}

.finance-tabs .ant-tabs-tab {
  font-weight: 600;
}

.tool-content {
  padding: 8px 0;
}

/* 工具概览 */
.tool-overview {
  margin-bottom: 32px;
}

.tool-info {
  padding: 24px;
  background: white;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
}

.tool-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 24px;
}

.tool-icon-large {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
  flex-shrink: 0;
}

.tool-details {
  flex: 1;
}

.tool-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #262626;
  margin-bottom: 8px;
}

.tool-description {
  color: #595959;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 12px;
}

.tool-tags {
  display: flex;
  gap: 8px;
}

.service-targets,
.policy-basis {
  margin-bottom: 20px;
}

.service-targets h4,
.policy-basis h4 {
  color: #262626;
  font-weight: 600;
  margin-bottom: 12px;
}

.target-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.target-item {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #52c41a;
  display: flex;
  align-items: center;
  gap: 6px;
}

.policy-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.policy-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.policy-tag:hover {
  transform: scale(1.05);
}

/* 工具操作区域 */
.tool-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.regulator-card {
  margin-top: 16px;
}

.regulator-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.regulator-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #fafafa;
  border-radius: 8px;
}

.regulator-icon {
  font-size: 1.2rem;
}

.regulator-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #262626;
}

/* 产品列表 */
.products-section {
  margin-top: 32px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.section-header h3 {
  color: #262626;
  font-weight: 600;
  margin: 0;
}

.product-card {
  height: 100%;
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.product-cover {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  color: white;
}

.product-icon {
  font-size: 2rem;
}

.product-risk {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  backdrop-filter: blur(4px);
}

.product-details {
  padding: 16px 0;
}

.product-meta {
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 0.9rem;
}

.meta-label {
  color: #8c8c8c;
}

.meta-value {
  color: #262626;
  font-weight: 600;
}

.product-features {
  margin-bottom: 16px;
}

.product-actions {
  display: flex;
  gap: 8px;
}

/* 服务区域 */
.services-section {
  padding: 0 24px;
  margin-bottom: 24px;
}

.matching-card,
.esg-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.matching-form {
  margin-bottom: 24px;
}

.matching-results {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 8px;
}

.result-icon {
  font-size: 1.5rem;
}

.result-info {
  flex: 1;
}

.result-title {
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.result-match {
  font-size: 0.8rem;
  color: #52c41a;
}

/* ESG评价 */
.esg-dashboard {
  padding: 8px 0;
}

.esg-score {
  display: flex;
  justify-content: space-around;
  margin-bottom: 24px;
}

.score-item {
  text-align: center;
}

.score-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px;
  position: relative;
}

.score-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.score-label {
  font-size: 0.8rem;
  color: #8c8c8c;
}

.environmental-benefits {
  margin-bottom: 24px;
}

.environmental-benefits h4 {
  color: #262626;
  font-weight: 600;
  margin-bottom: 16px;
}

.benefit-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 8px;
}

.benefit-icon {
  font-size: 1.5rem;
}

.benefit-content {
  flex: 1;
}

.benefit-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #262626;
  margin-bottom: 2px;
}

.benefit-unit {
  font-size: 0.8rem;
  color: #8c8c8c;
}

/* 机构和案例 */
.institutions-section {
  padding: 0 24px;
  margin-bottom: 24px;
}

.institutions-card,
.cases-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.institutions-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.institution-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.institution-item:hover {
  background: #f0f8ff;
  transform: translateY(-2px);
}

.institution-logo {
  width: 60px;
  height: 60px;
  background: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.institution-info {
  flex: 1;
}

.institution-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.institution-type {
  color: #8c8c8c;
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.institution-stats {
  display: flex;
  gap: 8px;
}

.institution-rating {
  text-align: right;
}

.rating-text {
  display: block;
  margin-top: 4px;
  font-size: 0.8rem;
  color: #8c8c8c;
}

/* 成功案例 */
.cases-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.case-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #fafafa;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.case-item:hover {
  background: #f6ffed;
  transform: translateY(-2px);
}

.case-image {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #52c41a, #95de64);
  border-radius: 8px;
  position: relative;
  flex-shrink: 0;
}

.case-type-badge {
  position: absolute;
  bottom: 4px;
  left: 4px;
  right: 4px;
  background: rgba(255, 255, 255, 0.9);
  color: #52c41a;
  font-size: 0.7rem;
  font-weight: 600;
  text-align: center;
  padding: 2px;
  border-radius: 4px;
}

.case-content {
  flex: 1;
}

.case-title {
  font-size: 1rem;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
  line-height: 1.3;
}

.case-summary {
  color: #8c8c8c;
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 8px;
}

.case-metrics {
  margin-bottom: 8px;
}

.metric {
  font-size: 0.8rem;
  margin-bottom: 2px;
}

.metric-label {
  color: #8c8c8c;
}

.metric-value {
  color: #262626;
  font-weight: 600;
  margin-left: 4px;
}

.case-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .header-stats {
    justify-content: center;
  }
  
  .finance-tools-section,
  .services-section,
  .institutions-section {
    padding: 0 16px;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .tool-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .benefit-grid {
    grid-template-columns: 1fr;
  }
  
  .esg-score {
    flex-direction: column;
    gap: 16px;
  }
  
  .institution-item {
    flex-direction: column;
    text-align: center;
  }
}
</style> 