<template>
  <div class="supply-chain-platform">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <span class="title-icon">🔗</span>
            产业供应链服务平台
          </h1>
          <p class="page-subtitle">10大服务模块助力绿色供应链发展 - 九大模块第7模块</p>
        </div>
        <div class="header-stats">
          <a-statistic title="供应链企业" :value="1456" suffix="家" />
          <a-statistic title="合作项目" :value="234" suffix="个" />
          <a-statistic title="交易金额" :value="56.7" suffix="亿元" />
        </div>
      </div>
    </div>

    <!-- 10大服务模块展示 -->
    <div class="services-section">
      <a-card class="services-card">
        <template #title>
          <div class="card-title">
            <span class="card-icon">⚙️</span>
            十大供应链服务模块
          </div>
        </template>
        
        <a-row :gutter="[24, 24]">
          <a-col 
            :xs="24" :sm="12" :md="8" :lg="6"
            v-for="(service, index) in supplyChainServices" 
            :key="service.code"
          >
            <a-card hoverable class="service-card" @click="selectService(service)">
              <template #cover>
                <div class="service-cover" :style="{ background: getServiceGradient(index) }">
                  <div class="service-icon">{{ service.icon }}</div>
                  <div class="service-badge">{{ service.code }}</div>
                </div>
              </template>
              
              <a-card-meta :title="service.name" :description="service.description" />
              
              <div class="service-metrics">
                <div class="metric-item">
                  <span class="metric-label">参与企业:</span>
                  <span class="metric-value">{{ service.companyCount }}</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">服务案例:</span>
                  <span class="metric-value">{{ service.caseCount }}</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">成功率:</span>
                  <a-progress 
                    :percent="service.successRate" 
                    size="small" 
                    :stroke-color="getSuccessColor(service.successRate)"
                  />
                </div>
              </div>
              
              <div class="service-keywords">
                <a-tag
                  v-for="keyword in service.keywords"
                  :key="keyword"
                  size="small"
                  color="green"
                >
                  {{ keyword }}
                </a-tag>
              </div>
              
              <div class="service-actions">
                <a-button type="primary" size="small" block @click.stop="enterService(service)">
                  <RightOutlined />
                  进入服务
                </a-button>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 供应链管理和服务 -->
    <div class="management-section">
      <a-row :gutter="[24, 24]">
        <!-- 供应链网络可视化 -->
        <a-col :xs="24" :lg="16">
          <a-card title="供应链网络可视化" class="network-card">
            <div class="network-container">
              <div class="network-placeholder">
                <div class="network-center">🏭 核心企业</div>
                <div class="network-nodes">
                  <div class="network-node supplier">📦 供应商</div>
                  <div class="network-node manufacturer">🔧 制造商</div>
                  <div class="network-node distributor">🚚 分销商</div>
                  <div class="network-node retailer">🏪 零售商</div>
                </div>
                <div class="network-info">
                  绿色供应链生态网络图谱
                </div>
              </div>
            </div>
            
            <!-- 供应链企业列表 -->
            <div class="companies-section">
              <h4>核心合作企业</h4>
              <a-list
                :data-source="supplyChainCompanies"
                :grid="{ gutter: 16, xs: 1, sm: 2, lg: 3 }"
                class="companies-list"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-card size="small" hoverable class="company-card">
                      <div class="company-header">
                        <div class="company-logo" :style="{ background: item.logoColor }">
                          {{ item.logo }}
                        </div>
                        <div class="company-type">{{ item.type }}</div>
                      </div>
                      
                      <div class="company-info">
                        <h4 class="company-name">{{ item.name }}</h4>
                        <p class="company-business">{{ item.business }}</p>
                        
                        <div class="company-metrics">
                          <div class="company-metric">
                            <span class="metric-label">合作项目:</span>
                            <span class="metric-value">{{ item.projects }}</span>
                          </div>
                          <div class="company-metric">
                            <span class="metric-label">信用评级:</span>
                            <a-rate :value="item.rating" disabled size="small" />
                          </div>
                        </div>
                        
                        <div class="company-tags">
                          <a-tag v-for="tag in item.tags" :key="tag" size="small">
                            {{ tag }}
                          </a-tag>
                        </div>
                        
                        <div class="company-actions">
                          <a-button type="primary" size="small" @click="contactCompany(item)">
                            <MessageOutlined />
                            联系合作
                          </a-button>
                        </div>
                      </div>
                    </a-card>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>
        
        <!-- 服务功能和工具 -->
        <a-col :xs="24" :lg="8">
          <a-space direction="vertical" style="width: 100%">
            <!-- 绿色采购服务 -->
            <a-card title="绿色采购服务" size="small" class="procurement-card">
              <div class="procurement-content">
                <div class="procurement-stats">
                  <div class="stat-box">
                    <div class="stat-number">1,234</div>
                    <div class="stat-label">绿色产品</div>
                  </div>
                  <div class="stat-box">
                    <div class="stat-number">567</div>
                    <div class="stat-label">认证供应商</div>
                  </div>
                </div>
                
                <div class="procurement-form">
                  <a-form layout="vertical" size="small">
                    <a-form-item label="采购类别">
                      <a-select placeholder="选择采购类别">
                        <a-select-option value="raw_materials">原材料</a-select-option>
                        <a-select-option value="equipment">设备设施</a-select-option>
                        <a-select-option value="services">技术服务</a-select-option>
                      </a-select>
                    </a-form-item>
                    
                    <a-form-item label="环保要求">
                      <a-checkbox-group>
                        <a-checkbox value="carbon_neutral">碳中和</a-checkbox>
                        <a-checkbox value="recyclable">可回收</a-checkbox>
                        <a-checkbox value="energy_saving">节能</a-checkbox>
                      </a-checkbox-group>
                    </a-form-item>
                    
                    <a-form-item>
                      <a-button type="primary" block @click="searchGreenProducts">
                        <SearchOutlined />
                        搜索绿色产品
                      </a-button>
                    </a-form-item>
                  </a-form>
                </div>
              </div>
            </a-card>
            
            <!-- 风险评估工具 -->
            <a-card title="供应链风险评估" size="small" class="risk-card">
              <div class="risk-assessment">
                <div class="risk-indicators">
                  <div class="risk-item">
                    <div class="risk-label">环境风险</div>
                    <div class="risk-level low">低</div>
                  </div>
                  <div class="risk-item">
                    <div class="risk-label">合规风险</div>
                    <div class="risk-level medium">中</div>
                  </div>
                  <div class="risk-item">
                    <div class="risk-label">质量风险</div>
                    <div class="risk-level low">低</div>
                  </div>
                  <div class="risk-item">
                    <div class="risk-label">财务风险</div>
                    <div class="risk-level high">高</div>
                  </div>
                </div>
                
                <div class="risk-score">
                  <div class="score-circle">
                    <div class="score-value">72</div>
                    <div class="score-label">综合评分</div>
                  </div>
                </div>
                
                <a-button type="primary" block @click="generateRiskReport">
                  <FileTextOutlined />
                  生成风险报告
                </a-button>
              </div>
            </a-card>
            
            <!-- 碳足迹追踪 -->
            <a-card title="碳足迹追踪" size="small" class="carbon-card">
              <div class="carbon-tracking">
                <div class="carbon-chart">
                  📈 供应链碳足迹分析图表
                </div>
                
                <div class="carbon-stats">
                  <div class="carbon-item">
                    <span class="carbon-label">原材料阶段:</span>
                    <span class="carbon-value">45% (2.3t CO₂)</span>
                  </div>
                  <div class="carbon-item">
                    <span class="carbon-label">生产制造:</span>
                    <span class="carbon-value">35% (1.8t CO₂)</span>
                  </div>
                  <div class="carbon-item">
                    <span class="carbon-label">运输配送:</span>
                    <span class="carbon-value">20% (1.0t CO₂)</span>
                  </div>
                </div>
                
                <div class="carbon-actions">
                  <a-button block @click="optimizeCarbon">
                    <EnvironmentOutlined />
                    优化建议
                  </a-button>
                </div>
              </div>
            </a-card>
            
            <!-- 智能匹配 -->
            <a-card title="智能供应商匹配" size="small" class="matching-card">
              <div class="matching-content">
                <div class="matching-criteria">
                  <h4>匹配条件</h4>
                  <div class="criteria-tags">
                    <a-tag color="blue">新能源</a-tag>
                    <a-tag color="green">ISO14001</a-tag>
                    <a-tag color="orange">碳中和</a-tag>
                  </div>
                </div>
                
                <div class="matching-results">
                  <h4>推荐供应商</h4>
                  <div class="supplier-list">
                    <div class="supplier-item" v-for="supplier in recommendedSuppliers" :key="supplier.id">
                      <div class="supplier-name">{{ supplier.name }}</div>
                      <div class="supplier-match">匹配度: {{ supplier.matchRate }}%</div>
                    </div>
                  </div>
                </div>
                
                <a-button type="primary" block @click="advancedMatching">
                  <ThunderboltOutlined />
                  高级匹配
                </a-button>
              </div>
            </a-card>
          </a-space>
        </a-col>
      </a-row>
    </div>

    <!-- 行业动态和案例分享 -->
    <div class="industry-section">
      <a-row :gutter="[24, 24]">
        <!-- 行业动态 -->
        <a-col :xs="24" :lg="12">
          <a-card title="供应链行业动态" class="industry-news-card">
            <a-list
              :data-source="industryNews"
              class="news-list"
            >
              <template #renderItem="{ item }">
                <a-list-item class="news-item" @click="readNews(item)">
                  <div class="news-content">
                    <div class="news-header">
                      <a-tag :color="item.categoryColor" size="small">{{ item.category }}</a-tag>
                      <span class="news-time">{{ item.time }}</span>
                    </div>
                    <h4 class="news-title">{{ item.title }}</h4>
                    <p class="news-summary">{{ item.summary }}</p>
                    <div class="news-metrics">
                      <span><EyeOutlined /> {{ item.views }}</span>
                      <span><LikeOutlined /> {{ item.likes }}</span>
                    </div>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
        
        <!-- 成功案例 -->
        <a-col :xs="24" :lg="12">
          <a-card title="绿色供应链成功案例" class="cases-card">
            <a-list
              :data-source="successCases"
              class="cases-list"
            >
              <template #renderItem="{ item }">
                <a-list-item class="case-item" @click="viewCase(item)">
                  <div class="case-content">
                    <div class="case-header">
                      <h4 class="case-title">{{ item.title }}</h4>
                      <div class="case-industry">{{ item.industry }}</div>
                    </div>
                    
                    <p class="case-description">{{ item.description }}</p>
                    
                    <div class="case-results">
                      <div class="result-item">
                        <span class="result-label">减排效果:</span>
                        <span class="result-value">{{ item.emission }}</span>
                      </div>
                      <div class="result-item">
                        <span class="result-label">成本节约:</span>
                        <span class="result-value">{{ item.savings }}</span>
                      </div>
                    </div>
                    
                    <div class="case-tags">
                      <a-tag v-for="tag in item.tags" :key="tag" size="small" color="blue">
                        {{ tag }}
                      </a-tag>
                    </div>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { 
  RightOutlined,
  MessageOutlined,
  SearchOutlined,
  FileTextOutlined,
  EnvironmentOutlined,
  ThunderboltOutlined,
  EyeOutlined,
  LikeOutlined
} from '@ant-design/icons-vue'

// 10大供应链服务模块
const supplyChainServices = ref([
  {
    code: 'S1',
    name: '绿色采购服务',
    description: '提供绿色产品目录、供应商认证、采购流程优化',
    icon: '🛒',
    companyCount: 567,
    caseCount: 234,
    successRate: 92,
    keywords: ['绿色产品', '供应商认证', '采购优化']
  },
  {
    code: 'S2',
    name: '供应商管理',
    description: '供应商评估、认证、培训、绩效管理一体化服务',
    icon: '👥',
    companyCount: 432,
    caseCount: 189,
    successRate: 88,
    keywords: ['供应商评估', '绩效管理', '培训认证']
  },
  {
    code: 'S3',
    name: '物流配送优化',
    description: '绿色物流、路径优化、碳足迹追踪、包装优化',
    icon: '🚛',
    companyCount: 345,
    caseCount: 156,
    successRate: 85,
    keywords: ['绿色物流', '路径优化', '碳足迹']
  },
  {
    code: 'S4',
    name: '库存管理',
    description: '智能库存预测、零库存管理、库存风险控制',
    icon: '📦',
    companyCount: 298,
    caseCount: 134,
    successRate: 89,
    keywords: ['智能预测', '零库存', '风险控制']
  },
  {
    code: 'S5',
    name: '质量管控',
    description: '全链条质量监控、检测认证、不合格品处理',
    icon: '✅',
    companyCount: 456,
    caseCount: 198,
    successRate: 94,
    keywords: ['质量监控', '检测认证', '品质管理']
  },
  {
    code: 'S6',
    name: '风险评估',
    description: '供应链风险识别、评估、预警、应急处理',
    icon: '⚠️',
    companyCount: 234,
    caseCount: 89,
    successRate: 87,
    keywords: ['风险识别', '预警系统', '应急处理']
  },
  {
    code: 'S7',
    name: '合同管理',
    description: '合同模板、电子签署、履约监控、争议处理',
    icon: '📋',
    companyCount: 378,
    caseCount: 167,
    successRate: 91,
    keywords: ['电子签署', '履约监控', '争议处理']
  },
  {
    code: 'S8',
    name: '财务结算',
    description: '供应链金融、账期管理、风险控制、资金优化',
    icon: '💰',
    companyCount: 289,
    caseCount: 123,
    successRate: 86,
    keywords: ['供应链金融', '账期管理', '资金优化']
  },
  {
    code: 'S9',
    name: '数据分析',
    description: '供应链数据可视化、性能分析、优化建议',
    icon: '📊',
    companyCount: 345,
    caseCount: 178,
    successRate: 90,
    keywords: ['数据可视化', '性能分析', '优化建议']
  },
  {
    code: 'S10',
    name: '可持续发展',
    description: '碳足迹追踪、循环经济、ESG评价、可持续报告',
    icon: '🌱',
    companyCount: 234,
    caseCount: 156,
    successRate: 93,
    keywords: ['碳足迹', '循环经济', 'ESG评价']
  }
])

// 供应链企业数据
const supplyChainCompanies = ref([
  {
    id: 1,
    name: '比亚迪股份有限公司',
    type: '核心企业',
    business: '新能源汽车制造',
    logo: '🚗',
    logoColor: '#1890ff',
    projects: 23,
    rating: 5,
    tags: ['新能源', 'ISO14001', '碳中和']
  },
  {
    id: 2,
    name: '宁德时代',
    type: '核心供应商',
    business: '动力电池供应',
    logo: '🔋',
    logoColor: '#52c41a',
    projects: 18,
    rating: 5,
    tags: ['电池技术', '绿色制造', '回收利用']
  },
  {
    id: 3,
    name: '顺丰速运',
    type: '物流合作商',
    business: '绿色物流服务',
    logo: '📦',
    logoColor: '#faad14',
    projects: 15,
    rating: 4,
    tags: ['绿色物流', '电动车队', '包装优化']
  }
])

// 推荐供应商
const recommendedSuppliers = ref([
  { id: 1, name: '隆基绿能科技', matchRate: 95 },
  { id: 2, name: '中材科技', matchRate: 87 },
  { id: 3, name: '三一重能', matchRate: 83 }
])

// 行业动态
const industryNews = ref([
  {
    id: 1,
    category: '政策解读',
    categoryColor: 'blue',
    title: '《绿色供应链管理指南》国家标准正式实施',
    summary: '新标准为企业建立绿色供应链管理体系提供了具体指导...',
    time: '3小时前',
    views: 1234,
    likes: 89
  },
  {
    id: 2,
    category: '技术创新',
    categoryColor: 'green',
    title: '区块链技术在供应链溯源中的应用突破',
    summary: '基于区块链的供应链溯源系统实现全链条透明化...',
    time: '8小时前',
    views: 856,
    likes: 67
  }
])

// 成功案例
const successCases = ref([
  {
    id: 1,
    title: '某汽车制造企业绿色供应链改造',
    industry: '汽车制造',
    description: '通过供应商绿色认证、物流优化等措施，实现全链条减排',
    emission: '减排30%',
    savings: '节约15%',
    tags: ['汽车制造', '减排', '成本节约']
  },
  {
    id: 2,
    title: '某电子企业循环供应链建设',
    industry: '电子制造',
    description: '建立产品回收体系，实现材料循环利用和废物零排放',
    emission: '减排45%',
    savings: '节约20%',
    tags: ['电子制造', '循环经济', '零排放']
  }
])

// 方法
const getServiceGradient = (index) => {
  const gradients = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)',
    'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)',
    'linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%)',
    'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'
  ]
  return gradients[index % gradients.length]
}

const getSuccessColor = (rate) => {
  if (rate >= 90) return '#52c41a'
  if (rate >= 80) return '#faad14'
  return '#f5222d'
}

const selectService = (service) => {
  console.log('选择服务:', service.name)
}

const enterService = (service) => {
  console.log('进入服务:', service.name)
}

const contactCompany = (company) => {
  console.log('联系企业:', company.name)
}

const searchGreenProducts = () => {
  console.log('搜索绿色产品')
}

const generateRiskReport = () => {
  console.log('生成风险报告')
}

const optimizeCarbon = () => {
  console.log('碳足迹优化建议')
}

const advancedMatching = () => {
  console.log('高级供应商匹配')
}

const readNews = (news) => {
  console.log('阅读新闻:', news.title)
}

const viewCase = (case_) => {
  console.log('查看案例:', case_.title)
}

onMounted(() => {
  console.log('产业供应链服务平台初始化完成')
})
</script>

<style scoped>
.supply-chain-platform {
  min-height: 100vh;
  background: var(--gradient-light);
}

/* 页面头部 */
.page-header {
  background: var(--gradient-primary);
  padding: 40px 24px;
  margin-bottom: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 2.5rem;
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  margin-bottom: 0;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.header-stats .ant-statistic {
  color: white;
}

.header-stats .ant-statistic-title {
  color: rgba(255, 255, 255, 0.8);
}

.header-stats .ant-statistic-content {
  color: white;
}

/* 服务模块区域 */
.services-section {
  padding: 0 24px;
  margin-bottom: 24px;
}

.services-card {
  border-radius: 16px;
  box-shadow: var(--box-shadow-medium);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.2rem;
  font-weight: 600;
}

.card-icon {
  font-size: 1.5rem;
}

.service-card {
  height: 100%;
  transition: var(--transition-base);
  cursor: pointer;
}

.service-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--box-shadow-heavy);
}

.service-cover {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  color: white;
}

.service-icon {
  font-size: 3rem;
}

.service-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.service-metrics {
  margin: 16px 0;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.metric-label {
  color: var(--text-secondary);
}

.metric-value {
  color: var(--text-primary);
  font-weight: 600;
}

.service-keywords {
  margin-bottom: 16px;
}

.service-actions {
  margin-top: 12px;
}

/* 管理区域 */
.management-section {
  padding: 0 24px;
  margin-bottom: 24px;
}

.network-card {
  border-radius: 16px;
  box-shadow: var(--box-shadow-medium);
}

.network-container {
  height: 300px;
  background: var(--background-light);
  border-radius: 12px;
  position: relative;
  margin-bottom: 24px;
}

.network-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.network-center {
  background: var(--gradient-primary);
  color: white;
  padding: 16px 24px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  position: relative;
  z-index: 2;
}

.network-nodes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.network-node {
  position: absolute;
  background: white;
  border: 2px solid var(--primary-color);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--primary-color);
}

.network-node.supplier {
  top: 20px;
  left: 20px;
}

.network-node.manufacturer {
  top: 20px;
  right: 20px;
}

.network-node.distributor {
  bottom: 20px;
  left: 20px;
}

.network-node.retailer {
  bottom: 20px;
  right: 20px;
}

.network-info {
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* 企业列表 */
.companies-section h4 {
  color: var(--text-primary);
  margin-bottom: 16px;
}

.companies-list .ant-list-item {
  padding: 8px;
}

.company-card {
  height: 100%;
  transition: var(--transition-base);
}

.company-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--box-shadow-medium);
}

.company-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.company-logo {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.company-type {
  background: var(--background-light);
  color: var(--text-secondary);
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
}

.company-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 6px;
}

.company-business {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-bottom: 12px;
}

.company-metrics {
  margin-bottom: 12px;
}

.company-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 0.8rem;
}

.company-tags {
  margin-bottom: 12px;
}

.company-actions {
  text-align: center;
}

/* 服务卡片 */
.procurement-card,
.risk-card,
.carbon-card,
.matching-card {
  border-radius: 16px;
  box-shadow: var(--box-shadow-medium);
  margin-bottom: 16px;
}

/* 绿色采购 */
.procurement-content {
  padding: 8px 0;
}

.procurement-stats {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-box {
  flex: 1;
  text-align: center;
  background: var(--background-light);
  padding: 12px;
  border-radius: 8px;
}

.stat-number {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.procurement-form {
  background: var(--background-light);
  padding: 16px;
  border-radius: 8px;
}

/* 风险评估 */
.risk-assessment {
  padding: 8px 0;
}

.risk-indicators {
  margin-bottom: 16px;
}

.risk-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.risk-label {
  font-size: 0.9rem;
  color: var(--text-primary);
}

.risk-level {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.risk-level.low {
  background: #f6ffed;
  color: #52c41a;
}

.risk-level.medium {
  background: #fff7e6;
  color: #faad14;
}

.risk-level.high {
  background: #fff2f0;
  color: #f5222d;
}

.risk-score {
  text-align: center;
  margin-bottom: 16px;
}

.score-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: conic-gradient(var(--primary-color) 0% 72%, #f0f0f0 72% 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px;
}

.score-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.score-label {
  font-size: 0.7rem;
  color: white;
}

/* 碳足迹追踪 */
.carbon-tracking {
  padding: 8px 0;
}

.carbon-chart {
  height: 100px;
  background: var(--background-light);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.carbon-stats {
  margin-bottom: 16px;
}

.carbon-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 0.85rem;
}

.carbon-label {
  color: var(--text-secondary);
}

.carbon-value {
  color: var(--text-primary);
  font-weight: 600;
}

/* 智能匹配 */
.matching-content {
  padding: 8px 0;
}

.matching-criteria {
  margin-bottom: 16px;
}

.matching-criteria h4 {
  font-size: 0.9rem;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.criteria-tags {
  margin-bottom: 16px;
}

.matching-results h4 {
  font-size: 0.9rem;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.supplier-list {
  margin-bottom: 16px;
}

.supplier-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: var(--background-light);
  border-radius: 8px;
  margin-bottom: 8px;
}

.supplier-name {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--text-primary);
}

.supplier-match {
  font-size: 0.8rem;
  color: var(--primary-color);
  font-weight: 600;
}

/* 行业动态和案例 */
.industry-section {
  padding: 0 24px;
  margin-bottom: 24px;
}

.industry-news-card,
.cases-card {
  border-radius: 16px;
  box-shadow: var(--box-shadow-medium);
}

.news-list,
.cases-list {
  max-height: 400px;
  overflow-y: auto;
}

.news-item,
.case-item {
  cursor: pointer;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  transition: var(--transition-fast);
}

.news-item:hover,
.case-item:hover {
  background: var(--background-light);
  transform: translateY(-2px);
}

.news-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.news-time {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.news-title,
.case-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 6px;
  line-height: 1.3;
}

.news-summary {
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: 8px;
}

.news-metrics {
  display: flex;
  gap: 16px;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* 案例卡片 */
.case-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.case-industry {
  background: var(--primary-color);
  color: white;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
}

.case-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: 12px;
}

.case-results {
  margin-bottom: 12px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 0.8rem;
}

.result-label {
  color: var(--text-secondary);
}

.result-value {
  color: var(--success-color);
  font-weight: 600;
}

.case-tags {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .header-stats {
    justify-content: center;
  }
  
  .services-section,
  .management-section,
  .industry-section {
    padding: 0 16px;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .procurement-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .risk-indicators {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }
  
  .company-metrics {
    flex-direction: column;
    gap: 4px;
  }
  
  .news-header,
  .case-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style> 