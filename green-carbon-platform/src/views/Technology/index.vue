<template>
  <div class="technology-platform">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <span class="title-icon">🔬</span>
            绿色技术创新平台
          </h1>
          <p class="page-subtitle">7大技术领域的创新服务 - 九大模块第6模块</p>
        </div>
        <div class="header-stats">
          <a-statistic title="技术项目" :value="890" suffix="个" />
          <a-statistic title="科研机构" :value="156" suffix="家" />
          <a-statistic title="专利成果" :value="2340" suffix="项" />
        </div>
      </div>
    </div>

    <!-- 7大技术领域展示 -->
    <div class="tech-domains-section">
      <a-card class="domains-card">
        <template #title>
          <div class="card-title">
            <span class="card-icon">🌟</span>
            七大绿色技术创新领域
          </div>
        </template>
        
        <a-row :gutter="[24, 24]">
          <a-col
            :xs="24" :sm="12" :md="8" :lg="6"
            v-for="(domain, index) in techDomains"
            :key="domain.code"
          >
            <a-card hoverable class="domain-card" @click="selectDomain(domain)">
              <template #cover>
                <div class="domain-cover" :style="{ background: getDomainGradient(index) }">
                  <div class="domain-icon">{{ domain.icon }}</div>
                  <div class="domain-badge">{{ domain.code }}</div>
                </div>
              </template>
              
              <a-card-meta :title="domain.name" :description="domain.description" />
              
              <div class="domain-stats">
                <div class="stat-item">
                  <span class="stat-label">项目数量:</span>
                  <span class="stat-value">{{ domain.projectCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">成功案例:</span>
                  <span class="stat-value">{{ domain.caseCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">技术成熟度:</span>
                  <a-progress 
                    :percent="domain.maturity" 
                    size="small" 
                    :stroke-color="getMaturityColor(domain.maturity)"
                  />
                </div>
              </div>
              
              <div class="domain-keywords">
                <a-tag
                  v-for="keyword in domain.keywords"
                  :key="keyword"
                  size="small"
                  color="blue"
                >
                  {{ keyword }}
                </a-tag>
              </div>
              
              <div class="domain-actions">
                <a-button type="primary" size="small" block @click.stop="exploreDomain(domain)">
                  <SearchOutlined />
                  探索技术
                </a-button>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'

// 7大技术领域
const techDomains = ref([
  {
    code: 'T1',
    name: '可再生能源技术',
    description: '太阳能、风能、水能等可再生能源发电和储能技术',
    icon: '☀️',
    projectCount: 156,
    caseCount: 89,
    maturity: 85,
    keywords: ['光伏', '风电', '储能', '智能电网']
  },
  {
    code: 'T2',
    name: '节能环保技术',
    description: '工业节能、建筑节能、环境治理等技术',
    icon: '🌿',
    projectCount: 134,
    caseCount: 67,
    maturity: 78,
    keywords: ['节能', '环保', '污染治理', '资源回收']
  },
  {
    code: 'T3',
    name: '新能源汽车技术',
    description: '电动汽车、氢燃料电池、充电设施等技术',
    icon: '🚗',
    projectCount: 98,
    caseCount: 45,
    maturity: 72,
    keywords: ['电动汽车', '氢能', '充电桩', '智能驾驶']
  },
  {
    code: 'T4',
    name: 'CCUS技术',
    description: '碳捕集、利用与封存技术',
    icon: '🌫️',
    projectCount: 67,
    caseCount: 23,
    maturity: 65,
    keywords: ['碳捕集', '碳封存', '碳利用', 'CO2转化']
  },
  {
    code: 'T5',
    name: '绿色制造技术',
    description: '清洁生产、循环经济、绿色材料等技术',
    icon: '🏭',
    projectCount: 89,
    caseCount: 34,
    maturity: 70,
    keywords: ['清洁生产', '循环经济', '绿色材料', '智能制造']
  },
  {
    code: 'T6',
    name: '智慧城市技术',
    description: '智能交通、绿色建筑、城市大脑等技术',
    icon: '🏙️',
    projectCount: 76,
    caseCount: 41,
    maturity: 68,
    keywords: ['智能交通', '绿色建筑', '城市大脑', '物联网']
  },
  {
    code: 'T7',
    name: '生态修复技术',
    description: '土壤修复、生态治理、生物多样性保护技术',
    icon: '🌳',
    projectCount: 54,
    caseCount: 28,
    maturity: 63,
    keywords: ['土壤修复', '生态治理', '生物保护', '湿地修复']
  }
])

// 方法
const getDomainGradient = (index) => {
  const gradients = [
    'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    'linear-gradient(135deg, #a8caba 0%, #5d4e75 100%)',
    'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)',
    'linear-gradient(135deg, #c1dfc4 0%, #deecdd 100%)',
    'linear-gradient(135deg, #fbc2eb 0%, #a6c1ee 100%)'
  ]
  return gradients[index % gradients.length]
}

const getMaturityColor = (maturity) => {
  if (maturity >= 80) return '#52c41a'
  if (maturity >= 60) return '#faad14'
  return '#f5222d'
}

const selectDomain = (domain) => {
  console.log('选择技术领域:', domain.name)
}

const exploreDomain = (domain) => {
  console.log('探索技术领域:', domain.name)
}

onMounted(() => {
  console.log('绿色技术创新平台初始化完成')
})
</script>

<style scoped>
.technology-platform {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #2c5f2d 0%, #97bc62 100%);
  padding: 40px 24px;
  margin-bottom: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 2.5rem;
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  margin-bottom: 0;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.header-stats .ant-statistic {
  color: white;
}

.header-stats .ant-statistic-title {
  color: rgba(255, 255, 255, 0.8);
}

.header-stats .ant-statistic-content {
  color: white;
}

/* 技术领域区域 */
.tech-domains-section {
  padding: 0 24px;
  margin-bottom: 24px;
}

.domains-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.2rem;
  font-weight: 600;
}

.card-icon {
  font-size: 1.5rem;
}

.domain-card {
  height: 100%;
  transition: all 0.3s ease;
  cursor: pointer;
}

.domain-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.domain-cover {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  color: white;
}

.domain-icon {
  font-size: 3rem;
}

.domain-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.9);
  color: #2c5f2d;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.domain-stats {
  margin: 16px 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.stat-label {
  color: #8c8c8c;
}

.stat-value {
  color: #262626;
  font-weight: 600;
}

.domain-keywords {
  margin-bottom: 16px;
}

.domain-actions {
  margin-top: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .header-stats {
    justify-content: center;
  }
  
  .tech-domains-section {
    padding: 0 16px;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
}
</style> 