<template>
  <div class="data-analysis-page">
    <a-card title="数据分析决策支持 (核心功能)" class="page-card">
      <p>基于九大模块数据的可视化分析与决策支持</p>
      
      <a-row :gutter="[16, 16]">
        <a-col :span="6" v-for="(metric, index) in keyMetrics" :key="index">
          <a-card size="small" class="metric-card">
            <a-statistic 
              :title="metric.title"
              :value="metric.value"
              :suffix="metric.suffix"
              :precision="metric.precision"
              :valueStyle="{ color: metric.color }"
            />
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col :span="12">
          <a-card size="small" title="模块数据分布" class="chart-card">
            <div class="chart-placeholder">
              <p>📊 九大模块数据分布饼图</p>
              <a-list size="small" :dataSource="moduleData">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>{{ item.module }}</template>
                      <template #description>{{ item.count }} 条数据</template>
                    </a-list-item-meta>
                    <template #actions>
                      <a-progress :percent="item.percentage" size="small" />
                    </template>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>
        
        <a-col :span="12">
          <a-card size="small" title="政策分类统计" class="chart-card">
            <div class="chart-placeholder">
              <p>📈 ZA-ZP政策分类统计柱状图</p>
              <a-list size="small" :dataSource="policyStats">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>{{ item.category }}</template>
                      <template #description>{{ item.count }} 个政策</template>
                    </a-list-item-meta>
                    <template #actions>
                      <a-tag :color="item.color">{{ item.growth }}%</a-tag>
                    </template>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col :span="24">
          <a-card size="small" title="决策支持建议" class="suggestions-card">
            <a-list :dataSource="suggestions">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-avatar :style="{ backgroundColor: item.color }">
                        {{ item.priority }}
                      </a-avatar>
                    </template>
                    <template #title>{{ item.title }}</template>
                    <template #description>{{ item.description }}</template>
                  </a-list-item-meta>
                  <template #actions>
                    <a-button type="primary" size="small">查看详情</a-button>
                  </template>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 关键指标
const keyMetrics = ref([
  {
    title: '总政策数量',
    value: 1234,
    suffix: '个',
    color: '#3f8600'
  },
  {
    title: '金融产品数量',
    value: 567,
    suffix: '个',
    color: '#cf1322'
  },
  {
    title: '技术项目数量',
    value: 890,
    suffix: '个',
    color: '#1890ff'
  },
  {
    title: '平均处理时间',
    value: 2.5,
    suffix: '天',
    precision: 1,
    color: '#722ed1'
  }
])

// 模块数据分布
const moduleData = ref([
  { module: '信息资源管理', count: 1234, percentage: 18 },
  { module: '政策法规服务', count: 2345, percentage: 32 },
  { module: '绿色金融服务', count: 567, percentage: 8 },
  { module: '绿色技术创新', count: 890, percentage: 12 },
  { module: '供应链服务', count: 1456, percentage: 20 },
  { module: '标杆示范', count: 456, percentage: 6 },
  { module: '创新服务', count: 234, percentage: 4 }
])

// 政策分类统计
const policyStats = ref([
  { category: 'ZA-综合政务', count: 156, growth: 15, color: 'green' },
  { category: 'ZB-经济管理', count: 234, growth: 23, color: 'blue' },
  { category: 'ZC-国土能源', count: 189, growth: 18, color: 'orange' },
  { category: 'ZD-工业交通', count: 123, growth: 12, color: 'purple' },
  { category: 'ZF-环境保护', count: 167, growth: 20, color: 'red' },
  { category: 'ZH-财政', count: 89, growth: 8, color: 'cyan' },
  { category: 'ZP-科技教育', count: 134, growth: 16, color: 'magenta' }
])

// 决策支持建议
const suggestions = ref([
  {
    priority: '高',
    title: '加强绿色金融产品创新',
    description: '基于11类金融工具分析，建议重点发展绿色信贷和绿色债券产品',
    color: '#f5222d'
  },
  {
    priority: '中',
    title: '优化政策分类体系',
    description: 'ZA-ZP分类体系中，综合政务类政策增长较快，建议细化分类标准',
    color: '#fa8c16'
  },
  {
    priority: '低',
    title: '完善标准库建设',
    description: '国家标准和行业标准覆盖面需要扩大，建议增加团体标准收录',
    color: '#52c41a'
  }
])
</script>

<style scoped>
.data-analysis-page {
  padding: 24px;
}

.metric-card {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-card {
  height: 400px;
}

.chart-placeholder {
  padding: 16px;
  text-align: center;
  color: #666;
}

.suggestions-card {
  min-height: 300px;
}
</style> 