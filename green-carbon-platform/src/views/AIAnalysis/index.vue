<template>
  <div class="ai-analysis-page">
    <a-card title="AI文档智能分析 (核心功能)" class="page-card">
      <p>基于框架结构标准的智能文档分析与分类</p>
      
      <a-row :gutter="[16, 16]">
        <a-col :span="12">
          <a-card size="small" title="智能分析能力" class="analysis-card">
            <div class="analysis-content">
              <a-list size="small" :dataSource="analysisCapabilities">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>{{ item.title }}</template>
                      <template #description>{{ item.description }}</template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>
        
        <a-col :span="12">
          <a-card size="small" title="分析结果统计" class="stats-card">
            <div class="stats-content">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-statistic title="已分析文档" :value="totalDocuments" />
                </a-col>
                <a-col :span="12">
                  <a-statistic title="准确率" :value="accuracy" suffix="%" />
                </a-col>
              </a-row>
              <a-row :gutter="16" style="margin-top: 16px;">
                <a-col :span="12">
                  <a-statistic title="自动分类" :value="autoClassified" />
                </a-col>
                <a-col :span="12">
                  <a-statistic title="元数据提取" :value="metadataExtracted" />
                </a-col>
              </a-row>
            </div>
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col :span="24">
          <a-card size="small" title="最新分析结果" class="results-card">
            <a-table :columns="columns" :dataSource="analysisResults" :pagination="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'category'">
                  <a-tag color="blue">{{ record.category }}</a-tag>
                </template>
                <template v-if="column.key === 'confidence'">
                  <a-progress :percent="record.confidence" size="small" />
                </template>
                <template v-if="column.key === 'status'">
                  <a-tag :color="record.status === '已完成' ? 'green' : 'orange'">
                    {{ record.status }}
                  </a-tag>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 智能分析能力
const analysisCapabilities = ref([
  {
    title: '自动分类识别',
    description: '基于ZA-ZP体系的政策文档自动分类'
  },
  {
    title: '元数据提取',
    description: '自动提取符合GB/T 21063.4标准的6项核心元数据'
  },
  {
    title: '关键词识别',
    description: '智能识别文档关键词和主题标签'
  },
  {
    title: '关联分析',
    description: '分析文档间的关联关系和影响链'
  },
  {
    title: '质量评估',
    description: '评估文档质量和完整性'
  }
])

// 统计数据
const totalDocuments = ref(1234)
const accuracy = ref(87.5)
const autoClassified = ref(1089)
const metadataExtracted = ref(1156)

// 表格列定义
const columns = [
  {
    title: '文档名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '分类结果',
    dataIndex: 'category',
    key: 'category',
  },
  {
    title: '置信度',
    dataIndex: 'confidence',
    key: 'confidence',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '分析时间',
    dataIndex: 'time',
    key: 'time',
  }
]

// 分析结果数据
const analysisResults = ref([
  {
    key: '1',
    name: '碳达峰碳中和实施方案',
    category: 'ZA-综合政务',
    confidence: 92,
    status: '已完成',
    time: '2025-01-15 14:30'
  },
  {
    key: '2',
    name: '绿色金融发展规划',
    category: 'ZB-经济管理',
    confidence: 88,
    status: '已完成',
    time: '2025-01-15 14:25'
  },
  {
    key: '3',
    name: '可再生能源发展指导意见',
    category: 'ZC-国土能源',
    confidence: 95,
    status: '已完成',
    time: '2025-01-15 14:20'
  },
  {
    key: '4',
    name: '工业节能减排技术规范',
    category: 'ZD-工业交通',
    confidence: 85,
    status: '处理中',
    time: '2025-01-15 14:15'
  }
])
</script>

<style scoped>
.ai-analysis-page {
  padding: 24px;
}

.analysis-card,
.stats-card,
.results-card {
  height: auto;
  min-height: 300px;
}

.analysis-content {
  height: 250px;
  overflow-y: auto;
}

.stats-content {
  padding: 16px 0;
}
</style> 