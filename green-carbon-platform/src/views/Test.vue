<template>
  <div style="padding: 20px;">
    <h1>测试页面</h1>
    <p>如果您能看到这个页面，说明路由系统工作正常。</p>
    
    <div style="margin-top: 20px;">
      <a-button type="primary" @click="$router.push('/resources')">
        测试信息资源管理
      </a-button>
      <a-button type="primary" @click="$router.push('/policy')" style="margin-left: 10px;">
        测试政策法规服务
      </a-button>
    </div>
    
    <div style="margin-top: 20px;">
      <h3>当前路由信息：</h3>
      <p>路径: {{ $route.path }}</p>
      <p>名称: {{ $route.name }}</p>
    </div>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'

const route = useRoute()
</script>
