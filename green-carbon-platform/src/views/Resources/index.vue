<template>
  <div class="resources-platform">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <span class="title-icon">📊</span>
            信息资源管理中心
          </h1>
          <p class="page-subtitle">基于GB/T 21063.4标准的6项核心元数据管理 - 九大模块核心</p>
        </div>
        <div class="header-stats">
          <a-statistic title="资源总数" :value="1234" suffix="个" />
          <a-statistic title="分类数量" :value="89" suffix="类" />
          <a-statistic title="今日新增" :value="23" suffix="个" />
        </div>
      </div>
    </div>

    <!-- 核心元数据管理区域 -->
    <div class="metadata-section">
      <a-card class="metadata-card">
        <template #title>
          <div class="card-title">
            <span class="card-icon">📋</span>
            GB/T 21063.4标准 - 6项核心元数据
          </div>
        </template>
        
        <a-row :gutter="[24, 24]">
          <!-- 元数据表单 -->
          <a-col :xs="24" :lg="12">
            <a-card title="新增资源" size="small" class="form-card">
              <a-form layout="vertical" :model="metadataForm" class="metadata-form">
                <a-form-item label="信息资源名称 (必选)" required>
                  <a-input 
                    v-model:value="metadataForm.resourceName" 
                    placeholder="请输入资源名称，最多100字符"
                    :maxlength="100"
                    show-count
                  />
                </a-form-item>
                
                <a-form-item label="信息资源摘要 (必选)" required>
                  <a-textarea 
                    v-model:value="metadataForm.resourceSummary" 
                    placeholder="请输入资源摘要，最多500字符"
                    :maxlength="500"
                    :rows="3"
                    show-count
                  />
                </a-form-item>
                
                <a-form-item label="三级分类代码 (必选)" required>
                  <a-cascader
                    v-model:value="metadataForm.classification"
                    :options="classificationOptions"
                    placeholder="请选择分类：ZA-01-001格式"
                    style="width: 100%"
                  />
                </a-form-item>
                
                <a-form-item label="关键词数组 (必选)" required>
                  <div class="keywords-input">
                    <a-input
                      v-model:value="keywordInput"
                      placeholder="输入关键词后按回车添加"
                      @pressEnter="addKeyword"
                    />
                    <div class="keywords-display">
                      <a-tag
                        v-for="(keyword, index) in metadataForm.keywords"
                        :key="index"
                        closable
                        @close="removeKeyword(index)"
                        color="blue"
                      >
                        {{ keyword }}
                      </a-tag>
                    </div>
                  </div>
                </a-form-item>
                
                <a-form-item label="提供方单位 (必选)" required>
                  <a-select 
                    v-model:value="metadataForm.provider" 
                    placeholder="请选择提供方单位"
                    style="width: 100%"
                  >
                    <a-select-option value="发改委">国家发展和改革委员会</a-select-option>
                    <a-select-option value="生态环境部">生态环境部</a-select-option>
                    <a-select-option value="工信部">工业和信息化部</a-select-option>
                    <a-select-option value="央行">中国人民银行</a-select-option>
                    <a-select-option value="科技部">科学技术部</a-select-option>
                  </a-select>
                </a-form-item>
                
                <a-form-item label="发布日期 (必选)" required>
                  <a-date-picker 
                    v-model:value="metadataForm.publishDate" 
                    placeholder="请选择发布日期"
                    style="width: 100%"
                  />
                </a-form-item>
                
                <a-form-item>
                  <a-space>
                    <a-button type="primary" @click="validateAndSubmit">
                      <SaveOutlined />
                      保存资源
                    </a-button>
                    <a-button @click="resetForm">
                      <ReloadOutlined />
                      重置表单
                    </a-button>
                    <a-button @click="validateMetadata">
                      <CheckOutlined />
                      验证标准
                    </a-button>
                  </a-space>
                </a-form-item>
              </a-form>
            </a-card>
          </a-col>
          
          <!-- 元数据验证器 -->
          <a-col :xs="24" :lg="12">
            <a-card title="标准验证器" size="small" class="validation-card">
              <div class="validation-content">
                <div class="schema-display">
                  <h4>GB/T 21063.4标准要求</h4>
                  <div class="schema-item" v-for="field in METADATA_SCHEMA.required" :key="field">
                    <div class="field-name">{{ getFieldLabel(field) }}</div>
                    <div class="field-status" :class="getValidationStatus(field)">
                      <a-icon :type="getValidationIcon(field)" />
                      {{ getValidationText(field) }}
                    </div>
                  </div>
                </div>
                
                <div class="compliance-score">
                  <div class="score-circle" :style="{ background: getScoreGradient() }">
                    <div class="score-value">{{ complianceScore }}%</div>
                  </div>
                  <div class="score-label">标准符合度</div>
                </div>
              </div>
            </a-card>
            
            <a-card title="三级分类体系" size="small" class="classification-card">
              <a-tree
                :tree-data="classificationTree"
                :expanded-keys="expandedKeys"
                @expand="onExpand"
                @select="onSelect"
                class="classification-tree"
              >
                <template #title="{ title, count }">
                  <span class="tree-title">
                    {{ title }}
                    <a-badge :count="count || 0" />
                  </span>
                </template>
              </a-tree>
            </a-card>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 资源管理和搜索 -->
    <div class="management-section">
      <a-row :gutter="[24, 24]">
        <!-- 智能检索系统 -->
        <a-col :xs="24" :lg="16">
          <a-card title="智能检索系统" class="search-card">
            <div class="search-container">
              <div class="main-search">
                <a-input-search
                  v-model:value="searchKeyword"
                  placeholder="全文检索：搜索资源名称、摘要、关键词..."
                  size="large"
                  @search="handleSearch"
                  enterButton
                  class="search-input"
                />
              </div>
              
              <div class="filter-section">
                <a-row :gutter="16">
                  <a-col :span="6">
                    <a-select v-model:value="searchFilters.classification" placeholder="分类筛选" style="width: 100%">
                      <a-select-option value="">全部分类</a-select-option>
                      <a-select-option value="ZA">ZA-综合政务</a-select-option>
                      <a-select-option value="ZB">ZB-经济管理</a-select-option>
                      <a-select-option value="ZC">ZC-国土能源</a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :span="6">
                    <a-select v-model:value="searchFilters.provider" placeholder="提供方筛选" style="width: 100%">
                      <a-select-option value="">全部机构</a-select-option>
                      <a-select-option value="发改委">发改委</a-select-option>
                      <a-select-option value="生态环境部">生态环境部</a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :span="6">
                    <a-range-picker v-model:value="searchFilters.dateRange" placeholder="时间范围" style="width: 100%" />
                  </a-col>
                  <a-col :span="6">
                    <a-space>
                      <a-button type="primary" @click="applySearch">
                        <SearchOutlined />
                        搜索
                      </a-button>
                      <a-button @click="resetSearch">
                        <ReloadOutlined />
                        重置
                      </a-button>
                    </a-space>
                  </a-col>
                </a-row>
              </div>
            </div>
            
            <!-- 搜索结果 -->
            <div class="search-results">
              <div class="results-header">
                <span>共找到 {{ searchResults.length }} 条资源</span>
                <a-select v-model:value="sortBy" style="width: 120px">
                  <a-select-option value="time">按时间</a-select-option>
                  <a-select-option value="name">按名称</a-select-option>
                  <a-select-option value="relevance">按相关性</a-select-option>
                </a-select>
              </div>
              
              <a-list
                :data-source="searchResults"
                :pagination="{ pageSize: 8, showSizeChanger: true }"
                class="results-list"
              >
                <template #renderItem="{ item }">
                  <a-list-item class="result-item">
                    <div class="result-content">
                      <div class="result-header">
                        <h4 class="result-title">{{ item.name }}</h4>
                        <div class="result-tags">
                          <a-tag color="blue">{{ item.classification }}</a-tag>
                          <a-tag color="green">{{ item.provider }}</a-tag>
                        </div>
                      </div>
                      
                      <div class="result-summary">{{ item.summary }}</div>
                      
                      <div class="result-meta">
                        <a-space>
                          <span><CalendarOutlined /> {{ item.publishDate }}</span>
                          <span><EyeOutlined /> {{ item.views }}</span>
                          <span><DownloadOutlined /> {{ item.downloads }}</span>
                        </a-space>
                      </div>
                      
                      <div class="result-keywords">
                        <a-tag v-for="keyword in item.keywords" :key="keyword" size="small">
                          {{ keyword }}
                        </a-tag>
                      </div>
                      
                      <div class="result-actions">
                        <a-space>
                          <a-button type="primary" size="small" @click="viewResource(item)">
                            <EyeOutlined />
                            查看详情
                          </a-button>
                          <a-button size="small" @click="editResource(item)">
                            <EditOutlined />
                            编辑
                          </a-button>
                          <a-button size="small" @click="downloadResource(item)">
                            <DownloadOutlined />
                            下载
                          </a-button>
                        </a-space>
                      </div>
                    </div>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>
        
        <!-- 数据质量监控 -->
        <a-col :xs="24" :lg="8">
          <a-space direction="vertical" style="width: 100%">
            <a-card title="数据质量监控" size="small" class="quality-card">
              <div class="quality-dashboard">
                <div class="quality-metrics">
                  <div class="metric-item">
                    <div class="metric-icon complete">✓</div>
                    <div class="metric-content">
                      <div class="metric-value">95.2%</div>
                      <div class="metric-label">元数据完整性</div>
                    </div>
                  </div>
                  
                  <div class="metric-item">
                    <div class="metric-icon standard">📏</div>
                    <div class="metric-content">
                      <div class="metric-value">98.7%</div>
                      <div class="metric-label">分类标准化</div>
                    </div>
                  </div>
                  
                  <div class="metric-item">
                    <div class="metric-icon duplicate">🔍</div>
                    <div class="metric-content">
                      <div class="metric-value">12</div>
                      <div class="metric-label">重复资源数</div>
                    </div>
                  </div>
                </div>
                
                <a-button type="primary" block @click="generateReport">
                  <FileTextOutlined />
                  生成质量报告
                </a-button>
              </div>
            </a-card>
            
            <a-card title="统计分析" size="small" class="stats-card">
              <div class="stats-chart">
                <div class="chart-placeholder">
                  📈 资源分布统计图表
                </div>
              </div>
              
              <div class="stats-summary">
                <div class="stat-row">
                  <span>政策类资源:</span>
                  <span>567个 (46%)</span>
                </div>
                <div class="stat-row">
                  <span>标准类资源:</span>
                  <span>345个 (28%)</span>
                </div>
                <div class="stat-row">
                  <span>技术类资源:</span>
                  <span>322个 (26%)</span>
                </div>
              </div>
            </a-card>
            
            <a-card title="热门提供方" size="small" class="providers-card">
              <a-list
                :data-source="topProviders"
                size="small"
                class="providers-list"
              >
                <template #renderItem="{ item, index }">
                  <a-list-item>
                    <div class="provider-item">
                      <div class="provider-rank">{{ index + 1 }}</div>
                      <div class="provider-info">
                        <div class="provider-name">{{ item.name }}</div>
                        <div class="provider-count">{{ item.count }} 个资源</div>
                      </div>
                    </div>
                  </a-list-item>
                </template>
              </a-list>
            </a-card>
          </a-space>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { 
  SaveOutlined, 
  ReloadOutlined, 
  CheckOutlined,
  SearchOutlined,
  CalendarOutlined,
  EyeOutlined,
  DownloadOutlined,
  EditOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import { METADATA_SCHEMA, CLASSIFICATION_TREE } from '@/constants/metadata'

// 响应式数据
const metadataForm = ref({
  resourceName: '',
  resourceSummary: '',
  classification: [],
  keywords: [],
  provider: '',
  publishDate: null
})

const keywordInput = ref('')
const searchKeyword = ref('')
const sortBy = ref('time')
const expandedKeys = ref(['ZA', 'ZB', 'ZC'])

const searchFilters = ref({
  classification: '',
  provider: '',
  dateRange: []
})

// 模拟数据
const searchResults = ref([
  {
    id: 1,
    name: '关于完整准确全面贯彻新发展理念做好碳达峰碳中和工作的意见',
    summary: '为完整准确全面贯彻新发展理念，做好碳达峰、碳中和工作，现提出如下意见...',
    classification: 'ZA-01-001',
    provider: '发改委',
    publishDate: '2021-10-24',
    keywords: ['碳达峰', '碳中和', '新发展理念'],
    views: 1523,
    downloads: 456
  },
  {
    id: 2,
    name: 'GB/T 32150-2015 工业企业温室气体排放核算和报告通则',
    summary: '本标准规定了工业企业温室气体排放核算和报告的基本原则、核算方法...',
    classification: 'ZC-03-002',
    provider: '生态环境部',
    publishDate: '2015-12-10',
    keywords: ['温室气体', '排放核算', '工业企业'],
    views: 967,
    downloads: 234
  }
])

const topProviders = ref([
  { name: '国家发展和改革委员会', count: 234 },
  { name: '生态环境部', count: 189 },
  { name: '工业和信息化部', count: 156 },
  { name: '中国人民银行', count: 123 },
  { name: '科学技术部', count: 98 }
])

// 分类选项
const classificationOptions = ref([
  {
    value: 'ZA',
    label: 'ZA-综合政务',
    children: [
      {
        value: '01',
        label: '01-工作机制',
        children: [
          { value: '001', label: '001-协调机制' },
          { value: '002', label: '002-管理办法' }
        ]
      }
    ]
  }
])

const classificationTree = ref(CLASSIFICATION_TREE)

// 计算属性
const complianceScore = computed(() => {
  const requiredFields = METADATA_SCHEMA.required
  const completedFields = requiredFields.filter(field => {
    const value = metadataForm.value[field]
    if (field === 'keywords') return value && value.length > 0
    if (field === 'classification') return value && value.length > 0
    return value && value.toString().trim() !== ''
  })
  return Math.round((completedFields.length / requiredFields.length) * 100)
})

// 方法
const getFieldLabel = (field) => {
  const labels = {
    resourceName: '信息资源名称',
    resourceSummary: '信息资源摘要',
    classification: '三级分类代码',
    keywords: '关键词数组',
    provider: '提供方单位',
    publishDate: '发布日期'
  }
  return labels[field] || field
}

const getValidationStatus = (field) => {
  const value = metadataForm.value[field]
  let isValid = false
  
  if (field === 'keywords') {
    isValid = value && value.length > 0
  } else if (field === 'classification') {
    isValid = value && value.length > 0
  } else {
    isValid = value && value.toString().trim() !== ''
  }
  
  return isValid ? 'valid' : 'invalid'
}

const getValidationIcon = (field) => {
  return getValidationStatus(field) === 'valid' ? 'check-circle' : 'close-circle'
}

const getValidationText = (field) => {
  return getValidationStatus(field) === 'valid' ? '已填写' : '待填写'
}

const getScoreGradient = () => {
  const score = complianceScore.value
  if (score >= 90) return 'conic-gradient(#52c41a 0% ' + score + '%, #f0f0f0 ' + score + '% 100%)'
  if (score >= 70) return 'conic-gradient(#faad14 0% ' + score + '%, #f0f0f0 ' + score + '% 100%)'
  return 'conic-gradient(#f5222d 0% ' + score + '%, #f0f0f0 ' + score + '% 100%)'
}

const addKeyword = () => {
  if (keywordInput.value.trim() && !metadataForm.value.keywords.includes(keywordInput.value.trim())) {
    metadataForm.value.keywords.push(keywordInput.value.trim())
    keywordInput.value = ''
  }
}

const removeKeyword = (index) => {
  metadataForm.value.keywords.splice(index, 1)
}

const validateAndSubmit = () => {
  console.log('提交资源:', metadataForm.value)
}

const resetForm = () => {
  metadataForm.value = {
    resourceName: '',
    resourceSummary: '',
    classification: [],
    keywords: [],
    provider: '',
    publishDate: null
  }
}

const validateMetadata = () => {
  console.log('验证标准，符合度:', complianceScore.value + '%')
}

const handleSearch = (value) => {
  console.log('搜索:', value)
}

const applySearch = () => {
  console.log('应用搜索条件:', searchFilters.value)
}

const resetSearch = () => {
  searchFilters.value = {
    classification: '',
    provider: '',
    dateRange: []
  }
  searchKeyword.value = ''
}

const onExpand = (keys) => {
  expandedKeys.value = keys
}

const onSelect = (keys, info) => {
  console.log('选择分类:', keys, info)
}

const viewResource = (item) => {
  console.log('查看资源:', item.name)
}

const editResource = (item) => {
  console.log('编辑资源:', item.name)
}

const downloadResource = (item) => {
  console.log('下载资源:', item.name)
}

const generateReport = () => {
  console.log('生成质量报告')
}

onMounted(() => {
  console.log('信息资源管理中心初始化完成')
})
</script>

<style scoped>
.resources-platform {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部 */
.page-header {
  background: var(--gradient-primary);
  padding: 40px 24px;
  margin-bottom: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 2.5rem;
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  margin-bottom: 0;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.header-stats .ant-statistic {
  color: white;
}

.header-stats .ant-statistic-title {
  color: rgba(255, 255, 255, 0.8);
}

.header-stats .ant-statistic-content {
  color: white;
}

/* 元数据管理区域 */
.metadata-section {
  padding: 0 24px;
  margin-bottom: 24px;
}

.metadata-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.2rem;
  font-weight: 600;
}

.card-icon {
  font-size: 1.5rem;
}

.form-card,
.validation-card,
.classification-card {
  height: 100%;
}

.metadata-form {
  padding: 8px 0;
}

.keywords-input {
  width: 100%;
}

.keywords-display {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 验证器样式 */
.validation-content {
  padding: 8px 0;
}

.schema-display {
  margin-bottom: 24px;
}

.schema-display h4 {
  color: #262626;
  font-weight: 600;
  margin-bottom: 16px;
}

.schema-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.field-name {
  font-weight: 600;
  color: #262626;
}

.field-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.9rem;
}

.field-status.valid {
  color: #52c41a;
}

.field-status.invalid {
  color: #f5222d;
}

.compliance-score {
  text-align: center;
}

.score-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px;
}

.score-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.score-label {
  font-size: 0.9rem;
  color: #8c8c8c;
}

/* 分类树 */
.classification-tree {
  max-height: 300px;
  overflow-y: auto;
}

.tree-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* 管理区域 */
.management-section {
  padding: 0 24px;
  margin-bottom: 24px;
}

.search-card,
.quality-card,
.stats-card,
.providers-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.search-container {
  margin-bottom: 24px;
}

.main-search {
  margin-bottom: 16px;
}

.search-input {
  font-size: 16px;
}

.filter-section {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

/* 搜索结果 */
.search-results {
  margin-top: 24px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.results-list .ant-list-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 0;
}

.result-item {
  width: 100%;
}

.result-content {
  width: 100%;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.result-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #262626;
  margin: 0;
  flex: 1;
  margin-right: 16px;
}

.result-tags {
  display: flex;
  gap: 8px;
}

.result-summary {
  color: #595959;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 12px;
}

.result-meta {
  margin-bottom: 12px;
}

.result-meta span {
  color: #8c8c8c;
  font-size: 0.85rem;
}

.result-keywords {
  margin-bottom: 16px;
}

.result-actions {
  text-align: left;
}

/* 质量监控 */
.quality-dashboard {
  padding: 8px 0;
}

.quality-metrics {
  margin-bottom: 24px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 8px;
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.metric-icon.complete {
  background: linear-gradient(135deg, #52c41a, #95de64);
}

.metric-icon.standard {
  background: linear-gradient(135deg, #1890ff, #69c0ff);
}

.metric-icon.duplicate {
  background: linear-gradient(135deg, #faad14, #ffc069);
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: #262626;
  margin-bottom: 2px;
}

.metric-label {
  font-size: 0.8rem;
  color: #8c8c8c;
}

/* 统计图表 */
.stats-chart {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.chart-placeholder {
  color: #8c8c8c;
  font-size: 1rem;
}

.stats-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
}

/* 提供方列表 */
.providers-list .ant-list-item {
  padding: 8px 0;
}

.provider-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.provider-rank {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: #2c5f2d;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.provider-info {
  flex: 1;
}

.provider-name {
  font-weight: 600;
  color: #262626;
  margin-bottom: 2px;
}

.provider-count {
  font-size: 0.8rem;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .header-stats {
    justify-content: center;
  }
  
  .metadata-section,
  .management-section {
    padding: 0 16px;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .result-title {
    margin-right: 0;
  }
  
  .filter-section .ant-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .filter-section .ant-col {
    width: 100% !important;
  }
}
</style> 