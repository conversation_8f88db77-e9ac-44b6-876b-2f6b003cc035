<template>
  <div class="resources-platform">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <span class="title-icon">📊</span>
            信息资源管理中心
          </h1>
          <p class="page-subtitle">基于GB/T 21063.4标准的6项核心元数据管理</p>
        </div>
        <div class="header-stats">
          <a-statistic title="资源总数" :value="1234" suffix="个" />
          <a-statistic title="分类数量" :value="89" suffix="类" />
          <a-statistic title="今日新增" :value="23" suffix="个" />
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="[24, 24]">
        <!-- 资源管理表单 -->
        <a-col :xs="24" :lg="12">
          <a-card title="新增资源" size="small">
            <a-form layout="vertical" :model="form">
              <a-form-item label="资源名称" name="name">
                <a-input v-model:value="form.name" placeholder="请输入资源名称" />
              </a-form-item>
              
              <a-form-item label="资源描述" name="description">
                <a-textarea v-model:value="form.description" placeholder="请输入资源描述" :rows="3" />
              </a-form-item>
              
              <a-form-item label="分类" name="category">
                <a-select v-model:value="form.category" placeholder="请选择分类">
                  <a-select-option value="ZA">ZA-综合政务</a-select-option>
                  <a-select-option value="ZB">ZB-经济管理</a-select-option>
                  <a-select-option value="ZC">ZC-社会管理</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="提供方" name="provider">
                <a-select v-model:value="form.provider" placeholder="请选择提供方">
                  <a-select-option value="发改委">国家发展和改革委员会</a-select-option>
                  <a-select-option value="生态环境部">生态环境部</a-select-option>
                  <a-select-option value="工信部">工业和信息化部</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item>
                <a-space>
                  <a-button type="primary" @click="handleSubmit">
                    保存资源
                  </a-button>
                  <a-button @click="handleReset">
                    重置表单
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>
        
        <!-- 资源列表 -->
        <a-col :xs="24" :lg="12">
          <a-card title="资源列表" size="small">
            <a-list :data-source="resources" item-layout="horizontal">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta
                    :title="item.name"
                    :description="item.description"
                  >
                    <template #avatar>
                      <a-avatar :style="{ backgroundColor: item.color }">
                        {{ item.category }}
                      </a-avatar>
                    </template>
                  </a-list-item-meta>
                  <template #actions>
                    <a-button size="small" type="link">查看</a-button>
                    <a-button size="small" type="link">编辑</a-button>
                  </template>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 表单数据
const form = ref({
  name: '',
  description: '',
  category: '',
  provider: ''
})

// 资源列表数据
const resources = ref([
  {
    id: 1,
    name: '碳达峰碳中和工作意见',
    description: '关于完整准确全面贯彻新发展理念做好碳达峰碳中和工作的意见',
    category: 'ZA',
    provider: '发改委',
    color: '#1890ff'
  },
  {
    id: 2,
    name: '温室气体排放核算标准',
    description: 'GB/T 32150-2015 工业企业温室气体排放核算和报告通则',
    category: 'ZB',
    provider: '生态环境部',
    color: '#52c41a'
  },
  {
    id: 3,
    name: '绿色金融指导意见',
    description: '关于构建绿色金融体系的指导意见',
    category: 'ZC',
    provider: '央行',
    color: '#faad14'
  }
])

// 方法
const handleSubmit = () => {
  console.log('提交表单:', form.value)
  // 这里可以添加提交逻辑
}

const handleReset = () => {
  form.value = {
    name: '',
    description: '',
    category: '',
    provider: ''
  }
}
</script>

<style scoped>
.resources-platform {
  width: 100%;
  min-height: calc(100vh - 64px);
  background: #f8f9fa;
}

.page-header {
  background: linear-gradient(135deg, #2c5f2d 0%, #689a3d 50%, #97bc62 100%);
  padding: 32px 24px;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.page-title {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 2.5rem;
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  margin-bottom: 0;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.header-stats .ant-statistic {
  color: white;
}

.header-stats .ant-statistic-title {
  color: rgba(255, 255, 255, 0.8);
}

.header-stats .ant-statistic-content {
  color: white;
}

.main-content {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .header-stats {
    justify-content: center;
  }
  
  .main-content {
    padding: 16px;
  }
}
</style>
