@import './base.css';

#app {
  width: 100vw;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  font-weight: normal;
  display: flex;
  flex-direction: column;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    margin: 0;
    padding: 0;
  }

  #app {
    width: 100vw;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
  }
}
