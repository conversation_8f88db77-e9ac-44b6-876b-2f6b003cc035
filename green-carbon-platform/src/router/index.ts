import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('../views/Home/index.vue'),
      meta: { title: '首页Dashboard', icon: 'HomeOutlined' },
    },
    {
      path: '/resources',
      name: 'Resources',
      component: () => import('../views/Resources/SimpleIndex.vue'),
      meta: { title: '信息资源管理中心', icon: 'DatabaseOutlined', module: 1 },
    },
    {
      path: '/policy',
      name: 'Policy',
      component: () => import('../views/Policy/SimpleIndex.vue'),
      meta: { title: '政策法规服务平台', icon: 'FileTextOutlined', module: 2 },
    },
    {
      path: '/finance',
      name: 'Finance',
      component: () => import('../views/Finance/index.vue'),
      meta: { title: '绿色金融服务中心', icon: 'DollarOutlined', module: 5 },
    },
    {
      path: '/technology',
      name: 'Technology',
      component: () => import('../views/Technology/index.vue'),
      meta: { title: '绿色技术创新平台', icon: 'ExperimentOutlined', module: 6 },
    },
    {
      path: '/supply-chain',
      name: 'SupplyChain',
      component: () => import('../views/SupplyChain/index.vue'),
      meta: { title: '产业供应链服务平台', icon: 'ShareAltOutlined', module: 7 },
    },
    {
      path: '/benchmark',
      name: 'Benchmark',
      component: () => import('../views/Benchmark/index.vue'),
      meta: { title: '标杆示范展示中心', icon: 'TrophyOutlined', module: 8 },
    },
    {
      path: '/innovation',
      name: 'Innovation',
      component: () => import('../views/Innovation/index.vue'),
      meta: { title: '综合创新服务中心', icon: 'BulbOutlined', module: 9 },
    },
    {
      path: '/ai-analysis',
      name: 'AIAnalysis',
      component: () => import('../views/AIAnalysis/index.vue'),
      meta: { title: 'AI文档智能分析', icon: 'RobotOutlined' },
    },
    {
      path: '/data-analysis',
      name: 'DataAnalysis',
      component: () => import('../views/DataAnalysis/index.vue'),
      meta: { title: '数据分析决策支持', icon: 'BarChartOutlined' },
    },
    {
      path: '/upload',
      name: 'Upload',
      component: () => import('../views/Upload/index.vue'),
      meta: { title: '文件上传管理中心', icon: 'UploadOutlined' },
    },
    {
      path: '/test',
      name: 'Test',
      component: () => import('../views/Test.vue'),
      meta: { title: '测试页面', icon: 'BugOutlined' },
    },
  ],
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta?.title || '页面'} - 国家绿色低碳全产业链服务平台`
  next()
})

export default router
